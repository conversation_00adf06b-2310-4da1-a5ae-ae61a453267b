import { Controller, Get, Param } from '@nestjs/common';
import { WorkSessionService } from './work-session.service';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('Work Sessions')
@Controller('work-sessions')
export class WorkSessionController {
  constructor(private readonly workSessionService: WorkSessionService) {}

  @Get()
  findAll() {
    return this.workSessionService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.workSessionService.findOne(+id);
  }

  @Get('worker/:workerId')
  findByWorker(@Param('workerId') workerId: string) {
    return this.workSessionService.findByWorker(+workerId);
  }

  @Get('factory/:factoryId')
  findByFactory(@Param('factoryId') factoryId: string) {
    return this.workSessionService.findByFactory(+factoryId);
  }
}
