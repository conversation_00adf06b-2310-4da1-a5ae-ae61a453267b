import { ApiProperty } from '@nestjs/swagger';

export class DamageDealerDto {
  @ApiProperty({
    description: 'User ID of the damage dealer',
    example: 123,
  })
  userId: number;

  @ApiProperty({
    description: 'Username of the damage dealer',
    example: 'WarriorSupreme',
  })
  username: string;

  @ApiProperty({
    description: 'Amount of damage dealt',
    example: 5000,
  })
  damage: number;

  @ApiProperty({
    description: 'Side the user fought on',
    example: 'attacker',
    enum: ['attacker', 'defender'],
  })
  side: 'attacker' | 'defender';
}
