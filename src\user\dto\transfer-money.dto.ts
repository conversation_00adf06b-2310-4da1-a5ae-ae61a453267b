import { ApiProperty } from '@nestjs/swagger';
import { IsInt, <PERSON>N<PERSON>ber, Min } from 'class-validator';

export class TransferMoneyDto {
  @ApiProperty({ description: 'Sender user ID' })
  @IsInt()
  fromUserId: number;

  @ApiProperty({ description: 'Receiver user ID' })
  @IsInt()
  toUserId: number;

  @ApiProperty({ description: 'Amount to transfer' })
  @IsNumber()
  @Min(0.01)
  amount: number;
}