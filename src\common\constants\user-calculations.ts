/**
 * Calculate learning time for a new perk upgrade.
 *
 * @param eduIndexResidency - Education index of the residency region.
 * @param newPerkLevel - The new perk level (affects learning time quadratically).
 * @param isPremium - Whether the user has premium status.
 * @param libraryBonus - Library bonus as a fraction (e.g., 0.2 for 20% bonus).
 * @returns The learning time in minutes.
 */
function calculateLearningTime(
  eduIndexResidency: number,
  newPerkLevel: number,
  isPremium: boolean,
  libraryBonus: number, // value between 0 and 1 representing percentage reduction
): number {
  // If education index is 11, adjust it to 20
  if (eduIndexResidency === 11) {
    eduIndexResidency = 20;
  }

  // Base constant determined from examples: using 100 gives 98 minutes for index 1.
  let time = (1 - eduIndexResidency / 50) * Math.pow(newPerkLevel, 2) * 100;

  // Apply premium discount
  if (isPremium) {
    time /= 2;
  }

  // Apply additional discounts based on new perk level thresholds
  if (newPerkLevel <= 100) {
    time /= 2;
  }
  if (newPerkLevel <= 50) {
    time /= 2;
  }

  // Apply Library bonus (reduces learning time)
  time = time * (1 - libraryBonus);

  return time;
}

// Example usage:
const eduIndex = 11; // Example: if region has index 11 (will be adjusted to 20)
const perkLevel = 1; // Example: starting perk level 1
const isPremiumUser = false; // Example: non-premium user
const libraryBonus = 0.1; // Example: 10% bonus (reduces time by 10%)

const learningTime = calculateLearningTime(
  eduIndex,
  perkLevel,
  isPremiumUser,
  libraryBonus,
);
console.log('Learning Time (minutes):', learningTime);
