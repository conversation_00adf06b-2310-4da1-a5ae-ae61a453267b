import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveDestinationStateFromTravelPermission1749500000000 implements MigrationInterface {
    name = 'RemoveDestinationStateFromTravelPermission1749500000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Drop the foreign key constraint first
        await queryRunner.query(`ALTER TABLE "travel_permission" DROP CONSTRAINT "FK_410d65b43f183341fcef1959810"`);
        
        // Drop the destinationStateId column
        await queryRunner.query(`ALTER TABLE "travel_permission" DROP COLUMN "destinationStateId"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Add the destinationStateId column back
        await queryRunner.query(`ALTER TABLE "travel_permission" ADD "destinationStateId" uuid`);
        
        // Add the foreign key constraint back
        await queryRunner.query(`ALTER TABLE "travel_permission" ADD CONSTRAINT "FK_410d65b43f183341fcef1959810" FOREIGN KEY ("destinationStateId") REFERENCES "state"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }
}
