const axios = require('axios');

const testLogin = async () => {
  console.log('Testing login with Chinese username...\n');

  const baseURL = 'http://localhost:3000';
  
  // Test data
  const testUser = {
    email: '<EMAIL>',
    password: 'password'
  };

  try {
    console.log('1. Attempting login...');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, testUser, {
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✓ Login successful');
    console.log('Response status:', loginResponse.status);
    console.log('Response data:', JSON.stringify(loginResponse.data, null, 2));
    
    // Extract token from response
    const token = loginResponse.data.access_token;
    const cookies = loginResponse.headers['set-cookie'];
    
    console.log('\n2. Token information:');
    console.log('Access token:', token ? 'PRESENT' : 'NOT_PRESENT');
    console.log('Token length:', token ? token.length : 0);
    console.log('Cookies set:', cookies ? cookies.length : 0);
    if (cookies) {
      console.log('Cookies:', cookies);
    }

    if (!token) {
      console.log('❌ No token received from login');
      return;
    }

    console.log('\n3. Testing API call with Authorization header...');
    try {
      const apiResponse = await axios.get(`${baseURL}/users/25`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      console.log('✓ API call with header successful');
      console.log('User data:', JSON.stringify(apiResponse.data, null, 2));
    } catch (headerError) {
      console.log('❌ API call with header failed:', headerError.response?.status, headerError.response?.data?.message);
    }

    console.log('\n4. Testing API call with cookies...');
    try {
      // Create axios instance with cookies
      const axiosWithCookies = axios.create({
        baseURL,
        withCredentials: true
      });

      // Set cookies manually if needed
      if (cookies) {
        axiosWithCookies.defaults.headers.Cookie = cookies.join('; ');
      }

      const apiResponse = await axiosWithCookies.get('/users/25');
      console.log('✓ API call with cookies successful');
      console.log('User data:', JSON.stringify(apiResponse.data, null, 2));
    } catch (cookieError) {
      console.log('❌ API call with cookies failed:', cookieError.response?.status, cookieError.response?.data?.message);
    }

  } catch (error) {
    console.log('❌ Login failed');
    console.log('Error status:', error.response?.status);
    console.log('Error message:', error.response?.data?.message || error.message);
    console.log('Full error:', JSON.stringify(error.response?.data, null, 2));
  }
};

// Check if server is running first
const checkServer = async () => {
  try {
    await axios.get('http://localhost:3000');
    console.log('✓ Server is running\n');
    return true;
  } catch (error) {
    console.log('❌ Server is not running or not accessible');
    console.log('Make sure the server is started with: npm run start:dev');
    return false;
  }
};

const main = async () => {
  const serverRunning = await checkServer();
  if (serverRunning) {
    await testLogin();
  }
};

main().catch(console.error);
