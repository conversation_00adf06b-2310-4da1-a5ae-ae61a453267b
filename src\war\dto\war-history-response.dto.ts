import { ApiProperty } from '@nestjs/swagger';
import { WarType, WarTarget, WarStatus } from '../entity/war.entity';
import { ResourcesCapturedDto } from './resources-captured.dto';
import { DamageDealerDto } from './damage-dealer.dto';
import { WarEventDto } from './war-event.dto';

export class WarHistoryResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the war history record',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Original war ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  warId: string;

  @ApiProperty({
    enum: WarType,
    description: 'Type of war',
    example: WarType.GROUND,
  })
  warType: WarType;

  @ApiProperty({
    enum: WarTarget,
    description: 'Target/objective of the war',
    example: WarTarget.CONQUEST,
  })
  warTarget: WarTarget;

  @ApiProperty({
    description: 'Official war declaration message',
    example: 'We hereby declare war to liberate the oppressed citizens...',
  })
  declaration: string;

  @ApiProperty({
    description: 'Name of the attacking state',
    example: 'Republic of Atlantis',
  })
  attackerStateName: string;

  @ApiProperty({
    description: 'Name of the attacking region',
    example: 'Northern Atlantis',
  })
  attackerRegionName: string;

  @ApiProperty({
    description: 'Name of the defending state',
    example: 'Kingdom of Pacifica',
  })
  defenderStateName: string;

  @ApiProperty({
    description: 'Name of the defending region',
    example: 'Western Pacifica',
  })
  defenderRegionName: string;

  @ApiProperty({
    description: 'Name of the target region (for conquest wars)',
    example: 'Southern Pacifica',
  })
  targetRegionName: string;

  @ApiProperty({
    description: 'Username of the user who declared the war',
    example: 'GeneralSupreme',
  })
  declaredByUsername: string;

  @ApiProperty({
    description: 'Date when the war was declared',
    type: Date,
  })
  declaredAt: Date;

  @ApiProperty({
    description: 'Date when the war started',
    type: Date,
    nullable: true,
  })
  startedAt: Date;

  @ApiProperty({
    description: 'Date when the war ended',
    type: Date,
    nullable: true,
  })
  endedAt: Date;

  @ApiProperty({
    description: 'Duration of the war in hours',
    example: 48.5,
  })
  durationHours: number;

  @ApiProperty({
    description: 'Final status of the war',
    enum: WarStatus,
    example: WarStatus.ENDED,
  })
  finalStatus: WarStatus;

  @ApiProperty({
    description: 'Side that won the war',
    example: 'attacker',
    enum: ['attacker', 'defender', null],
    nullable: true,
  })
  winner: 'attacker' | 'defender' | null;

  @ApiProperty({
    description: 'Total damage dealt by attackers',
    example: 25000,
  })
  totalAttackerDamage: number;

  @ApiProperty({
    description: 'Total damage dealt by defenders',
    example: 18000,
  })
  totalDefenderDamage: number;

  @ApiProperty({
    description: 'Total number of participants',
    example: 42,
  })
  totalParticipants: number;

  @ApiProperty({
    description: 'Number of attacker participants',
    example: 25,
  })
  attackerParticipants: number;

  @ApiProperty({
    description: 'Number of defender participants',
    example: 17,
  })
  defenderParticipants: number;

  @ApiProperty({
    description: 'Total energy spent by all participants',
    example: 5000,
  })
  totalEnergySpent: number;

  @ApiProperty({
    description: 'Resources captured (for resource wars)',
    type: ResourcesCapturedDto,
  })
  resourcesCaptured: ResourcesCapturedDto;

  @ApiProperty({
    description: 'Top 5 damage dealers in the war',
    type: [DamageDealerDto],
  })
  topDamageDealers: DamageDealerDto[];

  @ApiProperty({
    description: 'Key events during the war',
    type: [WarEventDto],
  })
  keyEvents: WarEventDto[];

  @ApiProperty({
    description: 'Date when the record was created',
    type: Date,
  })
  createdAt: Date;
}
