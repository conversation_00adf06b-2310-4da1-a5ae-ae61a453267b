import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1743612123477 implements MigrationInterface {
  name = 'Migrations1743612123477';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "gold" SET DEFAULT '1000'`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "money" SET DEFAULT '10000'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "money" SET DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "gold" SET DEFAULT '0'`,
    );
  }
}
