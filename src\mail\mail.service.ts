import { Injectable } from '@nestjs/common';
import { MailerService as NestMailerService } from '@nestjs-modules/mailer';
import { SendMailParams } from './interface/send-mail-params.interface';
import { SentMessageInfo } from 'nodemailer';

@Injectable()
export class MailService {
  constructor(private readonly nestMailerService: NestMailerService) {}

  async sendMail<TContext>({
    to,
    subject,
    template,
    context,
    attachments,
  }: SendMailParams<TContext>): Promise<SentMessageInfo> {
    const response = await this.nestMailerService.sendMail({
      to,
      subject,
      template: `./${template}`,
      context,
      attachments,
    });

    return response;
  }
}
