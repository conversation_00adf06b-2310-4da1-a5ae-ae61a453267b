import { Module, forwardRef } from '@nestjs/common';
import { AutoActionService } from './auto-action.service';
import { UserModule } from '../user/user.module';
import { WarModule } from 'src/war/war.module';

@Module({
  imports: [
    forwardRef(() => UserModule),
    forwardRef(() => WarModule),
  ],
  providers: [
    AutoActionService, // Keep the old service for now
  ],
  exports: [
    AutoActionService, // Keep exporting the old service for backward compatibility
  ],
})
export class SharedModule {}
