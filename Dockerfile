# 🛠 1st stage: Build the app
FROM node:23-alpine AS builder

WORKDIR /app

# Copy only package.json/package-lock.json first to install faster if no changes
COPY package*.json ./

# Install all dependencies
RUN npm install

# Copy the rest of the source code
COPY . .

# Build the app (output goes into /app/dist)
RUN npm run build


# 🚀 2nd stage: Only run built app
FROM node:23-alpine

WORKDIR /app

# Copy only production dependencies (optional optimization if you have devDependencies)
COPY package*.json ./
RUN npm install --omit=dev

# Copy compiled app from builder stage
COPY --from=builder /app/dist ./dist

# (Optional) Copy other necessary files like public folder, if any
# COPY --from=builder /app/public ./public

# Expose the port
EXPOSE 3000

# Command to run the app
CMD ["node", "dist/main"]
