import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTravelCostSystem1749400000000 implements MigrationInterface {
  name = 'AddTravelCostSystem1749400000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create travel mode enum
    await queryRunner.query(`CREATE TYPE "public"."travel_travelmode_enum" AS ENUM('regular', 'speed')`);
    
    // Add new columns to travel table
    await queryRunner.query(`ALTER TABLE "travel" ADD "travelMode" "public"."travel_travelmode_enum" NOT NULL DEFAULT 'regular'`);
    await queryRunner.query(`ALTER TABLE "travel" ADD "costPaid" double precision NOT NULL DEFAULT '0'`);
    await queryRunner.query(`ALTER TABLE "travel" ADD "currencyUsed" character varying NOT NULL DEFAULT 'money'`);
    
    // Add new columns to travel_permission table
    await queryRunner.query(`ALTER TABLE "travel_permission" ADD "travelMode" "public"."travel_travelmode_enum" NOT NULL DEFAULT 'regular'`);
    await queryRunner.query(`ALTER TABLE "travel_permission" ADD "costPaid" double precision NOT NULL DEFAULT '0'`);
    await queryRunner.query(`ALTER TABLE "travel_permission" ADD "currencyUsed" character varying NOT NULL DEFAULT 'money'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove columns from travel_permission table
    await queryRunner.query(`ALTER TABLE "travel_permission" DROP COLUMN "currencyUsed"`);
    await queryRunner.query(`ALTER TABLE "travel_permission" DROP COLUMN "costPaid"`);
    await queryRunner.query(`ALTER TABLE "travel_permission" DROP COLUMN "travelMode"`);
    
    // Remove columns from travel table
    await queryRunner.query(`ALTER TABLE "travel" DROP COLUMN "currencyUsed"`);
    await queryRunner.query(`ALTER TABLE "travel" DROP COLUMN "costPaid"`);
    await queryRunner.query(`ALTER TABLE "travel" DROP COLUMN "travelMode"`);
    
    // Drop travel mode enum
    await queryRunner.query(`DROP TYPE "public"."travel_travelmode_enum"`);
  }
}
