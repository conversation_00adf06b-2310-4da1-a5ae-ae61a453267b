import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ArrayMinSize,
  ArrayMaxSize,
  <PERSON><PERSON><PERSON>th,
  <PERSON><PERSON>ength,
} from 'class-validator';
import { ChatType } from '../entity/chat.entity';

export class CreateChatDto {
  @ApiProperty({
    enum: ChatType,
    description: 'Type of chat (direct or group)',
    example: ChatType.DIRECT,
  })
  @IsEnum(ChatType)
  type: ChatType;

  @ApiProperty({
    description: 'Chat name (required for group chats)',
    example: 'Strategy Discussion',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  name?: string;

  @ApiProperty({
    description: 'Chat description (optional for group chats)',
    example: 'Discuss war strategies and tactics',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiProperty({
    description: 'Array of participant user IDs',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(50) // Limit group size
  @IsNumber({}, { each: true })
  participantIds: number[];
}
