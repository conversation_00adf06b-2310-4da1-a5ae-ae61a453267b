import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsNotEmpty, IsOptional, IsString, IsEnum } from 'class-validator';
import { TravelMode } from '../entity/travel.entity';

export class InitiateTravelDto {
  @ApiProperty({
    description: 'The ID of the destination region',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  destinationRegionId: string;

  @ApiProperty({
    description: 'Optional permission ID if travel requires permission',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  permissionId?: string;

  @ApiProperty({
    description: 'Travel mode - regular (money cost) or speed (gold cost, 50% faster)',
    enum: TravelMode,
    example: TravelMode.REGULAR,
    required: false,
  })
  @IsEnum(TravelMode)
  @IsOptional()
  travelMode?: TravelMode = TravelMode.REGULAR;
}

export class TravelTimeEstimateDto {
  @ApiProperty({
    description: 'The ID of the source region',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  originRegionId: string;

  @ApiProperty({
    description: 'The ID of the destination region',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  destinationRegionId: string;

  @ApiProperty({
    description: 'Travel mode - regular (money cost) or speed (gold cost, 50% faster)',
    enum: TravelMode,
    example: TravelMode.REGULAR,
    required: false,
  })
  @IsEnum(TravelMode)
  @IsOptional()
  travelMode?: TravelMode = TravelMode.REGULAR;
}

export class RequestTravelPermissionDto {
  @ApiProperty({
    description: 'The ID of the destination region',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  destinationRegionId: string;

  @ApiProperty({
    description: 'Reason for travel request',
    example: 'Business trip',
    required: false,
  })
  @IsString()
  @IsOptional()
  reason?: string;

  @ApiProperty({
    description: 'Travel mode - regular (money cost) or speed (gold cost, 50% faster)',
    enum: TravelMode,
    example: TravelMode.REGULAR,
    required: false,
  })
  @IsEnum(TravelMode)
  @IsOptional()
  travelMode?: TravelMode = TravelMode.REGULAR;
}

export class RespondToPermissionRequestDto {
  @ApiProperty({
    description: 'Whether to approve or reject the request',
    example: true,
  })
  @IsNotEmpty()
  approve: boolean;

  @ApiProperty({
    description: 'Optional response message',
    example: 'Welcome to our state!',
    required: false,
  })
  @IsString()
  @IsOptional()
  responseMessage?: string;
}
