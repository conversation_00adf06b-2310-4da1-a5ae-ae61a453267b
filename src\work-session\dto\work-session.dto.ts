import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateWorkSessionDto {
  @ApiProperty({ description: 'ID of the factory to work at' })
  @IsUUID()
  factoryId: string;

  @ApiProperty({ description: 'ID of the worker' })
  @IsUUID()
  workerId: string;

  @ApiProperty({
    description: 'Amount of energy spent during the work session',
  })
  @IsNumber()
  @Min(0)
  energySpent: number;

  @ApiProperty({
    description: 'Amount of money earned during the work session',
  })
  @IsNumber()
  @Min(0)
  moneyEarned: number;

  @ApiProperty({
    description: 'Amount of resources earned during the work session',
  })
  @IsNumber()
  @Min(0)
  resourceEarned: number;
}
