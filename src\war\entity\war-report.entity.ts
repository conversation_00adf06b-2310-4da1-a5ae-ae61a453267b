import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
} from 'typeorm';
import { WarType, WarTarget } from './war.entity';
import { BattleStatisticsDto } from '../dto/battle-statistics.dto';
import { TimelineEventDto } from '../dto/timeline-event.dto';
import { EfficientDamageDealerDto } from '../dto/efficient-damage-dealer.dto';
import { WarOutcomeDto } from '../dto/war-outcome.dto';
import { VisualDataDto } from '../dto/visual-data.dto';

@Entity()
export class WarReport {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  warId: string;

  @Column()
  title: string;

  @Column('text')
  summary: string;

  @Column({
    type: 'enum',
    enum: WarType,
  })
  warType: WarType;

  @Column({
    type: 'enum',
    enum: WarTarget,
  })
  warTarget: WarTarget;

  @Column()
  startDate: Date;

  @Column()
  endDate: Date;

  @Column('float')
  durationHours: number;

  @Column()
  attackerName: string;

  @Column()
  defenderName: string;

  @Column('varchar')
  winner: 'attacker' | 'defender' | null;

  @Column('json')
  battleStatistics: BattleStatisticsDto;

  @Column('json')
  timeline: TimelineEventDto[];

  @Column('json')
  topDamageDealers: EfficientDamageDealerDto[];

  @Column('json')
  outcome: WarOutcomeDto;

  @Column('json', { nullable: true })
  visualData: VisualDataDto;

  @CreateDateColumn()
  createdAt: Date;
}
