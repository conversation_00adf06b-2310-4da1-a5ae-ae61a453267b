import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from '../../user/entity/user.entity';
import { Region } from '../../region/entity/region.entity';

export enum TravelStatus {
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum TravelMode {
  REGULAR = 'regular',
  SPEED = 'speed',
}

@Entity()
export class Travel {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User, (user) => user.travels)
  user: User;

  @ManyToOne(() => Region)
  sourceRegion: Region;

  @ManyToOne(() => Region)
  destinationRegion: Region;

  @Column({ type: 'timestamp' })
  startTime: Date;

  @Column({ type: 'timestamp' })
  endTime: Date;

  @Column({
    type: 'enum',
    enum: TravelStatus,
    default: TravelStatus.IN_PROGRESS,
  })
  status: TravelStatus;

  @Column('float')
  distance: number; // in kilometers

  @Column('float')
  travelTime: number; // in minutes

  @Column({ default: false })
  seaCrossing: boolean;

  @Column({ default: false })
  sameState: boolean;

  @Column({
    type: 'enum',
    enum: TravelMode,
    default: TravelMode.REGULAR,
  })
  travelMode: TravelMode;

  @Column('float', { default: 0 })
  costPaid: number;

  @Column({ default: 'money' })
  currencyUsed: string; // 'money' or 'gold'

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
