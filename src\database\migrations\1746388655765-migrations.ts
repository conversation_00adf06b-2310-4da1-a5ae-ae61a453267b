import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1746388655765 implements MigrationInterface {
    name = 'Migrations1746388655765'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "factory" DROP COLUMN "currentWorkers"`);
        await queryRunner.query(`ALTER TABLE "user" ADD "workingAtId" integer`);
        await queryRunner.query(`ALTER TABLE "user" ADD CONSTRAINT "FK_32f53b767e24b5d7d96ccb2230f" FOREIGN KEY ("workingAtId") REFERENCES "factory"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT "FK_32f53b767e24b5d7d96ccb2230f"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "workingAtId"`);
        await queryRunner.query(`ALTER TABLE "factory" ADD "currentWorkers" integer NOT NULL DEFAULT '0'`);
    }

}
