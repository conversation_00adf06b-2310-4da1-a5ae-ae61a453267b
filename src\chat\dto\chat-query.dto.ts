import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsOptional,
  IsN<PERSON>ber,
  Min,
  Max,
  IsString,
  IsUUID,
} from 'class-validator';

export class ChatQueryDto {
  @ApiProperty({
    description: 'Number of chats to return',
    example: 20,
    default: 20,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiProperty({
    description: 'Cursor for pagination (last chat ID from previous request)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  cursor?: string;
}

export class MessageQueryDto {
  @ApiProperty({
    description: 'Number of messages to return',
    example: 50,
    default: 50,
    minimum: 1,
    maximum: 100,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 50;

  @ApiProperty({
    description: 'Cursor for pagination (last message ID from previous request)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  cursor?: string;
}
