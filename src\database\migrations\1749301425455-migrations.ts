import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1749301425455 implements MigrationInterface {
    name = 'Migrations1749301425455'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "travel_permission" DROP CONSTRAINT "FK_410d65b43f183341fcef1959810"`);
        await queryRunner.query(`ALTER TABLE "state_election" DROP CONSTRAINT "FK_9396356395ce2d2614f01d77c1b"`);
        await queryRunner.query(`ALTER TABLE "travel_permission" ADD CONSTRAINT "FK_410d65b43f183341fcef1959810" FOREIGN KEY ("destinationStateId") REFERENCES "state"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "state_election" ADD CONSTRAINT "FK_9396356395ce2d2614f01d77c1b" FOREIGN KEY ("stateId") REFERENCES "state"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "state_election" DROP CONSTRAINT "FK_9396356395ce2d2614f01d77c1b"`);
        await queryRunner.query(`ALTER TABLE "travel_permission" DROP CONSTRAINT "FK_410d65b43f183341fcef1959810"`);
        await queryRunner.query(`ALTER TABLE "state_election" ADD CONSTRAINT "FK_9396356395ce2d2614f01d77c1b" FOREIGN KEY ("stateId") REFERENCES "state"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "travel_permission" ADD CONSTRAINT "FK_410d65b43f183341fcef1959810" FOREIGN KEY ("destinationStateId") REFERENCES "state"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
