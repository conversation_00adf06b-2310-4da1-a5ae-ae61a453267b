import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan } from 'typeorm';
import { User } from './entity/user.entity';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class PremiumExpirationService {
  private readonly logger = new Logger(PremiumExpirationService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * Check for expired premium subscriptions and update user status
   * This is run as a scheduled task every hour
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async checkExpiredPremiumSubscriptions(): Promise<void> {
    this.logger.log('Checking for expired premium subscriptions');

    try {
      const now = new Date();
      
      // Find users with expired premium subscriptions
      const expiredUsers = await this.userRepository.find({
        where: {
          isPremium: true,
          premiumExpiresAt: <PERSON><PERSON><PERSON>(now),
        },
        select: ['id', 'username', 'isPremium', 'subscriptionStatus', 'premiumExpiresAt'],
      });

      if (expiredUsers.length === 0) {
        this.logger.log('No expired premium subscriptions found');
        return;
      }

      this.logger.log(`Found ${expiredUsers.length} expired premium subscriptions`);

      // Update each expired user
      const updatePromises = expiredUsers.map(async (user) => {
        try {
          await this.userRepository.update(user.id, {
            isPremium: false,
            subscriptionStatus: 'inactive',
          });

          this.logger.log(
            `Updated user ${user.id} (${user.username}): premium expired at ${user.premiumExpiresAt?.toISOString()}`,
          );

          return { userId: user.id, username: user.username, success: true };
        } catch (error) {
          this.logger.error(
            `Failed to update user ${user.id} (${user.username}): ${error.message}`,
            error.stack,
          );
          return { userId: user.id, username: user.username, success: false, error: error.message };
        }
      });

      const results = await Promise.allSettled(updatePromises);
      
      const successful = results.filter(result => 
        result.status === 'fulfilled' && result.value.success
      ).length;
      
      const failed = results.length - successful;

      this.logger.log(
        `Premium expiration check completed: ${successful} users updated successfully, ${failed} failed`,
      );

      if (failed > 0) {
        this.logger.warn(`${failed} users failed to update during premium expiration check`);
      }

    } catch (error) {
      this.logger.error(
        `Error during premium expiration check: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Manual method to check and update expired premium subscriptions
   * Can be called from a controller for testing or manual execution
   */
  async manualCheckExpiredSubscriptions(): Promise<{
    totalChecked: number;
    expiredFound: number;
    successfulUpdates: number;
    failedUpdates: number;
    errors: string[];
  }> {
    this.logger.log('Manual premium expiration check initiated');

    const now = new Date();
    const errors: string[] = [];

    try {
      // Find all users with premium status
      const allPremiumUsers = await this.userRepository.find({
        where: { isPremium: true },
        select: ['id', 'username', 'isPremium', 'subscriptionStatus', 'premiumExpiresAt'],
      });

      // Filter expired users
      const expiredUsers = allPremiumUsers.filter(user => 
        user.premiumExpiresAt && user.premiumExpiresAt < now
      );

      if (expiredUsers.length === 0) {
        return {
          totalChecked: allPremiumUsers.length,
          expiredFound: 0,
          successfulUpdates: 0,
          failedUpdates: 0,
          errors: [],
        };
      }

      // Update expired users
      let successfulUpdates = 0;
      let failedUpdates = 0;

      for (const user of expiredUsers) {
        try {
          await this.userRepository.update(user.id, {
            isPremium: false,
            subscriptionStatus: 'inactive',
          });
          successfulUpdates++;
          this.logger.log(`Manually updated user ${user.id} (${user.username})`);
        } catch (error) {
          failedUpdates++;
          const errorMsg = `Failed to update user ${user.id} (${user.username}): ${error.message}`;
          errors.push(errorMsg);
          this.logger.error(errorMsg, error.stack);
        }
      }

      return {
        totalChecked: allPremiumUsers.length,
        expiredFound: expiredUsers.length,
        successfulUpdates,
        failedUpdates,
        errors,
      };

    } catch (error) {
      const errorMsg = `Error during manual premium expiration check: ${error.message}`;
      errors.push(errorMsg);
      this.logger.error(errorMsg, error.stack);

      return {
        totalChecked: 0,
        expiredFound: 0,
        successfulUpdates: 0,
        failedUpdates: 0,
        errors,
      };
    }
  }
}
