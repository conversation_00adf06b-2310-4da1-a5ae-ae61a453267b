import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsUUID,
  Min,
  Max,
  IsNumber,
  IsString,
  MinLength,
} from 'class-validator';

export class CreateUserDto {
  @ApiProperty({ example: 'player123', description: 'Unique username' })
  @IsString()
  @IsNotEmpty()
  username: string;

  @ApiProperty({ example: '<EMAIL>', description: 'User email' })
  @IsEmail()
  email: string;

  @ApiProperty({
    example: 'StrongPassword123',
    description: 'Password (hashed before storage)',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @ApiProperty({ example: 1, description: 'User level' })
  @IsNumber()
  @Min(1)
  level?: number = 1;

  @ApiProperty({ example: 100, description: 'User energy' })
  @IsNumber()
  @Min(0)
  @Max(100)
  energy?: number = 100;

  @ApiProperty({ example: 'Region-UUID', description: 'Region ID' })
  @IsUUID()
  regionId: string;

  @ApiProperty({ example: 'Residency-UUID', description: 'Residency ID' })
  @IsUUID()
  @IsOptional()
  residencyId?: string;

  @ApiProperty({ example: 'Party-UUID', description: 'Political party ID' })
  @IsUUID()
  @IsOptional()
  partyId?: string;

  @ApiProperty({ example: 'Democracy', description: 'User political views' })
  @IsString()
  @IsOptional()
  politicalViews?: string;
}

export class UpdateUserDto {
  @ApiProperty({ example: 'NewUsername', description: 'Updated username' })
  @IsString()
  @IsOptional()
  username?: string;

  @ApiProperty({ example: 10, description: 'Updated level' })
  @IsNumber()
  @Min(1)
  @IsOptional()
  level?: number;

  @ApiProperty({ example: 90, description: 'Updated energy level' })
  @IsNumber()
  @Min(0)
  @Max(100)
  @IsOptional()
  energy?: number;

  @ApiProperty({ example: 800, description: 'Updated gold balance' })
  @IsNumber()
  @Min(0)
  @IsOptional()
  gold?: number;

  @ApiProperty({ example: true, description: 'Premium status' })
  @IsOptional()
  isPremium?: boolean;

  @ApiProperty({ example: 'cus_123456789', description: 'Stripe customer ID' })
  @IsString()
  @IsOptional()
  stripeCustomerId?: string;

  @ApiProperty({
    example: 'sub_123456789',
    description: 'Stripe subscription ID',
  })
  @IsString()
  @IsOptional()
  stripeSubscriptionId?: string;

  @ApiProperty({
    example: 'active',
    description: 'Subscription status',
    enum: ['active', 'inactive', 'past_due', 'canceled', 'unpaid', 'trialing'],
  })
  @IsString()
  @IsOptional()
  subscriptionStatus?: string;

  @ApiProperty({
    example: '2023-12-31T23:59:59.999Z',
    description: 'Premium expiration date',
  })
  @IsOptional()
  premiumExpiresAt?: Date;
}
