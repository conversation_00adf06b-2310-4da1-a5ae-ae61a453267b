import { ApiProperty } from '@nestjs/swagger';
import { Exclude, Expose, Type } from 'class-transformer';
import { ChatType } from '../entity/chat.entity';
import { MessageType, MessageStatus } from '../entity/message.entity';

export class UserBasicDto {
  @ApiProperty({ description: 'User ID', example: 1 })
  id: number;

  @ApiProperty({ description: 'Username', example: 'player123' })
  username: string;

  @ApiProperty({ description: 'User level', example: 5 })
  level: number;

  @Exclude()
  email: string;

  @Exclude()
  password: string;
}

export class MessageResponseDto {
  @ApiProperty({
    description: 'Message ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Message content',
    example: 'Hello, how are you?',
  })
  content: string;

  @ApiProperty({
    enum: MessageType,
    description: 'Message type',
    example: MessageType.TEXT,
  })
  type: MessageType;

  @ApiProperty({
    enum: MessageStatus,
    description: 'Message status',
    example: MessageStatus.SENT,
  })
  status: MessageStatus;

  @ApiProperty({
    description: 'Message sender',
    type: () => UserBasicDto,
  })
  @Type(() => UserBasicDto)
  sender: UserBasicDto;

  @ApiProperty({
    description: 'Whether the message is read by current user',
    example: false,
  })
  @Expose()
  isRead: boolean;

  @ApiProperty({
    description: 'When the message was edited',
    example: '2024-01-15T10:30:00Z',
    required: false,
  })
  editedAt?: Date;

  @ApiProperty({
    description: 'When the message was created',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'When the message was last updated',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: Date;
}

export class ChatResponseDto {
  @ApiProperty({
    description: 'Chat ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    enum: ChatType,
    description: 'Chat type',
    example: ChatType.DIRECT,
  })
  type: ChatType;

  @ApiProperty({
    description: 'Chat name',
    example: 'Strategy Discussion',
    required: false,
  })
  name?: string;

  @ApiProperty({
    description: 'Chat description',
    example: 'Discuss war strategies',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'Chat participants',
    type: [UserBasicDto],
  })
  @Type(() => UserBasicDto)
  participants: UserBasicDto[];

  @ApiProperty({
    description: 'Last message in the chat',
    type: () => MessageResponseDto,
    required: false,
  })
  @Type(() => MessageResponseDto)
  lastMessage?: MessageResponseDto;

  @ApiProperty({
    description: 'Number of unread messages for current user',
    example: 3,
  })
  @Expose()
  unreadCount: number;

  @ApiProperty({
    description: 'When the last message was sent',
    example: '2024-01-15T10:30:00Z',
    required: false,
  })
  lastMessageAt?: Date;

  @ApiProperty({
    description: 'When the chat was created',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'When the chat was last updated',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: Date;
}

export class PaginatedChatsResponseDto {
  @ApiProperty({
    description: 'Array of chats',
    type: [ChatResponseDto],
  })
  @Type(() => ChatResponseDto)
  chats: ChatResponseDto[];

  @ApiProperty({
    description: 'Whether there are more chats available',
    example: true,
  })
  hasMore: boolean;

  @ApiProperty({
    description: 'Cursor for next page',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  nextCursor?: string;
}

export class PaginatedMessagesResponseDto {
  @ApiProperty({
    description: 'Array of messages',
    type: [MessageResponseDto],
  })
  @Type(() => MessageResponseDto)
  messages: MessageResponseDto[];

  @ApiProperty({
    description: 'Whether there are more messages available',
    example: true,
  })
  hasMore: boolean;

  @ApiProperty({
    description: 'Cursor for next page',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  nextCursor?: string;
}
