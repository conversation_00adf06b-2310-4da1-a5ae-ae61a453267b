import { IsEnum, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { WarType } from '../entity/war.entity';

export class AvailableTargetsQueryDto {
  @ApiProperty({
    description: 'Type of war to get available targets for',
    enum: WarType,
    example: WarType.GROUND,
  })
  @IsEnum(WarType)
  type: WarType;

  @ApiProperty({
    description: 'ID of the attacking region',
    example: 'uuid-string',
  })
  @IsUUID()
  attackingRegionId: string;
}
