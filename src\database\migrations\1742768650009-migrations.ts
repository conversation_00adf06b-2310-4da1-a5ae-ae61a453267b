import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1742768650009 implements MigrationInterface {
  name = 'Migrations1742768650009';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "perception"`);
    await queryRunner.query(
      `ALTER TABLE "user" ADD "gold" double precision NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD "money" double precision NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD "trainingExpiresAt" TIMESTAMP`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user" DROP COLUMN "trainingExpiresAt"`,
    );
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "money"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "gold"`);
    await queryRunner.query(
      `ALTER TABLE "user" ADD "perception" integer NOT NULL DEFAULT '0'`,
    );
  }
}
