import { ApiProperty } from '@nestjs/swagger';

export class ResourcesCapturedDto {
  @ApiProperty({
    description: 'Amount of gold captured',
    example: 5000,
    required: false,
  })
  gold?: number;

  @ApiProperty({
    description: 'Amount of oil captured',
    example: 2000,
    required: false,
  })
  oil?: number;

  @ApiProperty({
    description: 'Amount of ore captured',
    example: 3000,
    required: false,
  })
  ore?: number;

  @ApiProperty({
    description: 'Amount of uranium captured',
    example: 100,
    required: false,
  })
  uranium?: number;

  @ApiProperty({
    description: 'Amount of diamonds captured',
    example: 50,
    required: false,
  })
  diamonds?: number;
}
