import {
  <PERSON>,
  Get,
  Patch,
  Param,
  Request,
  NotFoundException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { NotificationService } from './notification.service';
import { Notification } from './entity/notification.entity';
import { NotificationResponseDto } from './dto/notification-response.dto';
import { Request as ExpressRequest } from 'express';

@ApiTags('Notifications')
@Controller('notifications')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @ApiOperation({ summary: 'Get all notifications for the authenticated user' })
  @ApiResponse({
    status: 200,
    description: 'List of all notifications',
    type: [NotificationResponseDto],
  })
  @Get()
  async getUserNotifications(
    @Request() req: ExpressRequest & { user: { userId: number } },
  ): Promise<Notification[]> {
    return this.notificationService.getUserNotifications(req.user.userId);
  }

  @ApiOperation({
    summary: 'Get unread notifications for the authenticated user',
  })
  @ApiResponse({
    status: 200,
    description: 'List of unread notifications',
    type: [NotificationResponseDto],
  })
  @Get('unread')
  async getUnreadNotifications(
    @Request() req: ExpressRequest & { user: { userId: number } },
  ): Promise<Notification[]> {
    return this.notificationService.getUnreadNotifications(req.user.userId);
  }

  @ApiOperation({ summary: 'Mark a notification as read' })
  @ApiParam({ name: 'id', description: 'Notification ID' })
  @ApiResponse({
    status: 200,
    description: 'Notification marked as read',
    type: NotificationResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Notification not found',
  })
  @Patch(':id/read')
  async markAsRead(@Param('id') id: string): Promise<Notification> {
    try {
      return await this.notificationService.markAsRead(id);
    } catch (error) {
      throw new NotFoundException('Notification not found');
    }
  }

  @ApiOperation({ summary: 'Mark all notifications as read' })
  @ApiResponse({
    status: 200,
    description: 'All notifications marked as read',
  })
  @Patch('mark-all-read')
  async markAllAsRead(
    @Request() req: ExpressRequest & { user: { userId: number } },
  ): Promise<{ message: string }> {
    await this.notificationService.markAllAsRead(req.user.userId);
    return { message: 'All notifications marked as read' };
  }
}
