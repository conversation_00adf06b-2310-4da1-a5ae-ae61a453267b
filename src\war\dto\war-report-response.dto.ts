import { ApiProperty } from '@nestjs/swagger';
import { WarType, WarTarget } from '../entity/war.entity';
import { BattleStatisticsDto } from './battle-statistics.dto';
import { TimelineEventDto } from './timeline-event.dto';
import { EfficientDamageDealerDto } from './efficient-damage-dealer.dto';
import { WarOutcomeDto } from './war-outcome.dto';
import { VisualDataDto } from './visual-data.dto';

export class WarReportResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the war report',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Original war ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  warId: string;

  @ApiProperty({
    description: 'Title of the war report',
    example: 'The Great Northern Conflict',
  })
  title: string;

  @ApiProperty({
    description: 'Summary of the war',
    example:
      'A brief but intense conflict that resulted in the Republic of Atlantis conquering Western Pacifica.',
  })
  summary: string;

  @ApiProperty({
    enum: WarType,
    description: 'Type of war',
    example: WarType.GROUND,
  })
  warType: WarType;

  @ApiProperty({
    enum: WarTarget,
    description: 'Target/objective of the war',
    example: WarTarget.CONQUEST,
  })
  warTarget: WarTarget;

  @ApiProperty({
    description: 'Date when the war started',
    type: Date,
  })
  startDate: Date;

  @ApiProperty({
    description: 'Date when the war ended',
    type: Date,
  })
  endDate: Date;

  @ApiProperty({
    description: 'Duration of the war in hours',
    example: 48.5,
  })
  durationHours: number;

  @ApiProperty({
    description: 'Name of the attacking state/region',
    example: 'Republic of Atlantis',
  })
  attackerName: string;

  @ApiProperty({
    description: 'Name of the defending state/region',
    example: 'Kingdom of Pacifica',
  })
  defenderName: string;

  @ApiProperty({
    description: 'Side that won the war',
    example: 'attacker',
    enum: ['attacker', 'defender'],
  })
  winner: 'attacker' | 'defender';

  @ApiProperty({
    description: 'Detailed battle statistics',
    type: BattleStatisticsDto,
  })
  battleStatistics: BattleStatisticsDto;

  @ApiProperty({
    description: 'Detailed timeline of the war',
    type: [TimelineEventDto],
  })
  timeline: TimelineEventDto[];

  @ApiProperty({
    description: 'Top damage dealers in the war',
    type: [EfficientDamageDealerDto],
  })
  topDamageDealers: EfficientDamageDealerDto[];

  @ApiProperty({
    description: 'Outcome and consequences of the war',
    type: WarOutcomeDto,
  })
  outcome: WarOutcomeDto;

  @ApiProperty({
    description: 'Maps and visual data for the war',
    type: VisualDataDto,
  })
  visualData: VisualDataDto;

  @ApiProperty({
    description: 'Date when the report was generated',
    type: Date,
  })
  createdAt: Date;
}
