import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WorkSessionController } from './work-session.controller';
import { WorkSessionService } from './work-session.service';
import { WorkSession } from './entity/work-session.entity';
import { Factory } from '../factory/entity/factory.entity';
import { User } from '../user/entity/user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([WorkSession, User, Factory])],
  controllers: [WorkSessionController],
  providers: [WorkSessionService],
  exports: [WorkSessionService],
})
export class WorkSessionModule {}
