import { ApiProperty } from '@nestjs/swagger';

export class PaymentIntentResponseDto {
  @ApiProperty({
    description: 'Client secret for the payment intent',
    example: 'pi_1234567890_secret_1234567890',
  })
  clientSecret: string;

  @ApiProperty({
    description: 'Payment intent ID',
    example: 'pi_1234567890',
  })
  paymentIntentId: string;

  @ApiProperty({
    description: 'Amount of gold to be purchased',
    example: 1000,
  })
  goldAmount: number;

  @ApiProperty({
    description:
      'Amount to be charged in the smallest currency unit (e.g., cents)',
    example: 999,
  })
  amount: number;

  @ApiProperty({
    description: 'Currency for the payment',
    example: 'usd',
  })
  currency: string;
}
