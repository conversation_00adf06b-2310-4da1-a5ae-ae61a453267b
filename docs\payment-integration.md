# Payment Integration Guide

This document provides instructions for setting up and configuring the payment system for Warfront Nations.

## Prerequisites

1. Create a Stripe account at [stripe.com](https://stripe.com)
2. Install the Stripe CLI for local webhook testing (optional but recommended)

## Configuration

### 1. Set up Stripe Products and Prices

1. Log in to your Stripe Dashboard
2. Go to Products > Create Product
3. Create the following products:

#### Premium Subscription
- Create a product named "Premium Subscription"
- Add three pricing options:
  - Monthly: Recurring payment, $4.99/month
  - Semi-annual: Recurring payment, $24.99/6 months
  - Yearly: Recurring payment, $49.99/year
- Note the price IDs for all options

### 2. Configure Environment Variables

Add the following variables to your `.env` file:

```
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret
STRIPE_PREMIUM_MONTHLY_PRICE_ID=price_your_monthly_price_id
STRIPE_PREMIUM_YEARLY_PRICE_ID=price_your_yearly_price_id
```

### 3. Set up Webhook Endpoint

#### For Local Development:
1. Install the Stripe CLI: [Installation Guide](https://stripe.com/docs/stripe-cli)
2. Run the following command to forward webhooks to your local server:
   ```
   stripe listen --forward-to localhost:3001/payments/webhook
   ```
3. The CLI will provide a webhook signing secret. Add this to your `.env` file as `STRIPE_WEBHOOK_SECRET`.

#### For Production:
1. In the Stripe Dashboard, go to Developers > Webhooks
2. Add an endpoint with the URL: `https://your-api-domain.com/payments/webhook`
3. Select the following events to listen for:
   - `payment_intent.succeeded`
   - `payment_intent.failed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.paid`
   - `invoice.payment_failed`
4. Copy the signing secret and add it to your production environment variables as `STRIPE_WEBHOOK_SECRET`

## Testing the Integration

### Gold Purchase
1. Use the `/payments/gold/create-intent` endpoint to create a payment intent
2. Use Stripe test cards to complete the payment:
   - Success: `4242 4242 4242 4242`
   - Failure: `4000 0000 0000 0002`

### Premium Subscription
1. Use the `/payments/premium/create-session` endpoint to create a checkout session
2. Complete the checkout using a test card
3. Verify the subscription status using the `/payments/premium/verify` endpoint

## Webhook Events

The payment system handles the following webhook events:

- `payment_intent.succeeded`: Updates user's gold balance after successful gold purchase
- `customer.subscription.created`: Activates premium subscription for the user
- `customer.subscription.updated`: Updates subscription status
- `customer.subscription.deleted`: Deactivates premium subscription

## Security Considerations

- All payment-related API endpoints are protected with authentication except for the webhook endpoint
- The webhook endpoint verifies the signature of incoming requests using the webhook secret
- Sensitive payment information is never stored in our database
- All payment processing is handled by Stripe
