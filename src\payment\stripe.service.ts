import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Strip<PERSON> from 'stripe';

@Injectable()
export class StripeService {
  private stripe: Stripe;
  private readonly logger = new Logger(StripeService.name);

  constructor(private configService: ConfigService) {
    const stripeSecretKey = this.configService.get<string>('STRIPE_SECRET_KEY');
    if (!stripeSecretKey) {
      this.logger.error(
        'STRIPE_SECRET_KEY is not defined in environment variables',
      );
      throw new Error('STRIPE_SECRET_KEY is not defined');
    }
    this.stripe = new Stripe(stripeSecretKey, {
      apiVersion: '2023-10-16' as any,
    });
  }

  getStripe(): Stripe {
    return this.stripe;
  }

  async createPaymentIntent(
    amount: number,
    currency: string,
    metadata: Record<string, string>,
  ): Promise<Stripe.PaymentIntent> {
    try {
      return await this.stripe.paymentIntents.create({
        amount,
        currency,
        metadata,
        payment_method_types: ['card'],
      });
    } catch (error) {
      this.logger.error(
        `Error creating payment intent: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async createCheckoutSession(
    priceId: string,
    customerId: string,
    metadata: Record<string, string>,
    successUrl: string,
    cancelUrl: string,
  ): Promise<Stripe.Checkout.Session> {
    try {
      this.logger.log(
        `Creating checkout session with metadata: ${JSON.stringify(metadata)}`,
      );

      return await this.stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        success_url: successUrl,
        cancel_url: cancelUrl,
        customer: customerId,
        metadata,
        subscription_data: {
          metadata, // Also add metadata to the subscription that will be created
        },
      });
    } catch (error) {
      this.logger.error(
        `Error creating checkout session: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async createGoldCheckoutSession(
    priceId: string,
    customerId: string,
    metadata: Record<string, string>,
    successUrl: string,
    cancelUrl: string,
  ): Promise<Stripe.Checkout.Session> {
    try {
      this.logger.log(
        `Creating gold checkout session with metadata: ${JSON.stringify(metadata)}`,
      );

      return await this.stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        mode: 'payment',
        success_url: successUrl,
        cancel_url: cancelUrl,
        customer: customerId,
        metadata,
      });
    } catch (error) {
      this.logger.error(
        `Error creating gold checkout session: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async constructEventFromPayload(
    signature: string,
    payload: Buffer,
  ): Promise<Stripe.Event> {
    const webhookSecret = this.configService.get<string>(
      'STRIPE_WEBHOOK_SECRET',
    );
    if (!webhookSecret) {
      this.logger.error(
        'STRIPE_WEBHOOK_SECRET is not defined in environment variables',
      );
      throw new Error('STRIPE_WEBHOOK_SECRET is not defined');
    }

    try {
      return this.stripe.webhooks.constructEvent(
        payload,
        signature,
        webhookSecret,
      );
    } catch (error) {
      this.logger.error(
        `Error constructing webhook event: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async getCustomer(customerId: string): Promise<Stripe.Customer> {
    try {
      const customer = await this.stripe.customers.retrieve(customerId);

      // Check if the customer is deleted
      if ((customer as any).deleted) {
        this.logger.error(`Customer ${customerId} has been deleted in Stripe`);
        throw new Error(`Customer ${customerId} has been deleted in Stripe`);
      }

      return customer as Stripe.Customer;
    } catch (error) {
      // Log with more detailed information
      if (
        error.type === 'StripeInvalidRequestError' &&
        error.statusCode === 404
      ) {
        this.logger.error(
          `Customer ${customerId} not found in Stripe: ${error.message}`,
        );
      } else {
        this.logger.error(
          `Error retrieving customer ${customerId}: ${error.message}`,
          error.stack,
        );
      }
      throw error;
    }
  }

  async createCustomer(email: string, name: string): Promise<Stripe.Customer> {
    try {
      return await this.stripe.customers.create({
        email,
        name,
      });
    } catch (error) {
      this.logger.error(
        `Error creating customer: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async getSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    try {
      return await this.stripe.subscriptions.retrieve(subscriptionId);
    } catch (error) {
      this.logger.error(
        `Error retrieving subscription: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async getPaymentIntent(
    paymentIntentId: string,
  ): Promise<Stripe.PaymentIntent> {
    try {
      return await this.stripe.paymentIntents.retrieve(paymentIntentId);
    } catch (error) {
      this.logger.error(
        `Error retrieving payment intent: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async cancelSubscription(
    subscriptionId: string,
  ): Promise<Stripe.Subscription> {
    try {
      this.logger.log(`Canceling subscription: ${subscriptionId}`);
      return await this.stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: true,
      });
    } catch (error) {
      this.logger.error(
        `Error canceling subscription: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
