import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';
import { SKIP_AUTH_KEY } from '../../auth/decorators/skip-auth.decorator';
import { JwtDebugUtil } from '../utils/jwt-debug.util';

@Injectable()
export class AuthGuard implements CanActivate {
  private readonly logger = new Logger(AuthGuard.name);

  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
    private reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    // Check if the endpoint is marked as public or should skip auth
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    const skipAuth = this.reflector.getAllAndOverride<boolean>(SKIP_AUTH_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (
      isPublic ||
      skipAuth ||
      request.url.startsWith('/metrics') ||
      request.url === '/payments/webhook'
    ) {
      return true;
    }

    const token = this.extractTokenFromRequest(request);
    if (!token) {
      // Debug token extraction failure
      this.logger.warn(`No token found for ${request.method} ${request.url}`);
      this.logger.warn(`Authorization header: ${request.headers.authorization || 'NOT_PRESENT'}`);
      this.logger.warn(`Cookie access_token: ${request.cookies?.access_token ? 'PRESENT' : 'NOT_PRESENT'}`);
      this.logger.warn(`All cookies: ${JSON.stringify(request.cookies || {})}`);
      throw new UnauthorizedException('No authentication token provided');
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: this.configService.get('JWT_SECRET'),
      });

      // Debug payload for Chinese username issues
      if (process.env.NODE_ENV === 'development' || process.env.JWT_DEBUG === 'true') {
        JwtDebugUtil.debugJwtPayload(payload, 'AuthGuard');
      }

      // Validate payload structure
      if (!payload || typeof payload !== 'object' || !payload.userId) {
        this.logger.warn('Invalid JWT payload structure');
        JwtDebugUtil.debugJwtPayload(payload, 'Invalid Payload');
        throw new UnauthorizedException('Invalid token payload');
      }

      // Handle potential character encoding issues with username
      if (payload.username && typeof payload.username === 'string') {
        const originalUsername = payload.username;
        try {
          // Ensure username is properly decoded if it contains non-ASCII characters
          payload.username = Buffer.from(payload.username, 'utf8').toString('utf8');

          // Debug if username changed during encoding fix
          if (originalUsername !== payload.username && (process.env.NODE_ENV === 'development' || process.env.JWT_DEBUG === 'true')) {
            JwtDebugUtil.compareUsernames(originalUsername, payload.username, 'Original', 'Fixed');
          }
        } catch (encodingError) {
          this.logger.warn(`Username encoding issue for user ${payload.userId}: ${encodingError.message}`);
          JwtDebugUtil.debugUsername(originalUsername);
          // Continue with original username if encoding fix fails
          payload.username = originalUsername;
        }
      }

      // 💡 Assign the payload to the request object here
      // so that you can access it in your route handlers
      request['user'] = payload;
    } catch (error) {
      // Log specific error details for debugging
      if (error instanceof UnauthorizedException) {
        throw error;
      }

      // Debug token if verification fails and it might be encoding related
      if (process.env.NODE_ENV === 'development' || process.env.JWT_DEBUG === 'true') {
        JwtDebugUtil.debugJwtToken(token, 'Failed Verification');
      }

      this.logger.warn(`JWT verification failed: ${error.message}`);
      throw new UnauthorizedException('Invalid or expired token');
    }

    return true;
  }

  private extractTokenFromRequest(request: Request): string | undefined {
    const headerToken = request.headers.authorization?.split(' ')[1];
    const cookieToken = request.cookies['access_token'];

    // Debug token extraction
    if (process.env.NODE_ENV === 'development' || process.env.JWT_DEBUG === 'true') {
      this.logger.debug(`Token extraction for ${request.method} ${request.url}:`);
      this.logger.debug(`  Authorization header: ${request.headers.authorization || 'NOT_PRESENT'}`);
      this.logger.debug(`  Header token: ${headerToken || 'NOT_PRESENT'}`);
      this.logger.debug(`  Cookie token: ${cookieToken ? 'PRESENT' : 'NOT_PRESENT'}`);
      this.logger.debug(`  Final token: ${headerToken || cookieToken || 'NOT_PRESENT'}`);
    }

    // Priority to header token, fallback to cookie token
    return headerToken || cookieToken;
  }
}
