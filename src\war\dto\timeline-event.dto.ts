import { ApiProperty } from '@nestjs/swagger';

export class TimelineEventDto {
  @ApiProperty({
    description: 'Timestamp of the event',
    type: Date,
    example: '2023-04-15T14:30:00Z',
  })
  timestamp: Date;

  @ApiProperty({
    description: 'Description of the event',
    example: 'War declared by GeneralSupreme',
  })
  description: string;

  @ApiProperty({
    description: 'Amount of damage dealt (if applicable)',
    example: 1500,
    required: false,
  })
  damage?: number;

  @ApiProperty({
    description: 'Side that performed the action (if applicable)',
    example: 'attacker',
    enum: ['attacker', 'defender'],
    required: false,
  })
  side?: 'attacker' | 'defender';

  @ApiProperty({
    description: 'Type of event',
    example: 'declaration',
    enum: [
      'declaration',
      'start',
      'sea_phase_start',
      'sea_phase_end',
      'ground_phase_start',
      'battle',
      'end',
    ],
  })
  eventType: string;
}
