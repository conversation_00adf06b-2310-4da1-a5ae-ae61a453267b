import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1744067940863 implements MigrationInterface {
  name = 'Migrations1744067940863';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "state" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "description" character varying, "flagUrl" character varying, "treasury" double precision NOT NULL DEFAULT '0', "resourceReserves" json NOT NULL DEFAULT '{}', "allies" json NOT NULL DEFAULT '[]', "enemies" json NOT NULL DEFAULT '[]', "isActive" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "leaderId" integer, CONSTRAINT "PK_549ffd046ebab1336c3a8030a12" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."war_wartype_enum" AS ENUM('ground', 'sea', 'air', 'revolution')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."war_status_enum" AS ENUM('pending', 'sea_phase', 'ground_phase', 'ended')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."war_wartarget_enum" AS ENUM('conquest', 'resistance', 'resources', 'revolution')`,
    );
    await queryRunner.query(
      `CREATE TABLE "war" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "warType" "public"."war_wartype_enum" NOT NULL, "status" "public"."war_status_enum" NOT NULL DEFAULT 'pending', "warTarget" "public"."war_wartarget_enum" NOT NULL DEFAULT 'conquest', "declaration" text, "declaredAt" TIMESTAMP NOT NULL, "startedAt" TIMESTAMP, "endedAt" TIMESTAMP, "seaPhaseStartedAt" TIMESTAMP, "seaPhaseEndedAt" TIMESTAMP, "seaPhaseWon" boolean NOT NULL DEFAULT false, "groundPhaseStartedAt" TIMESTAMP, "attackerSeaDamage" double precision NOT NULL DEFAULT '0', "defenderSeaDamage" double precision NOT NULL DEFAULT '0', "attackerGroundDamage" double precision NOT NULL DEFAULT '0', "defenderGroundDamage" double precision NOT NULL DEFAULT '0', "revolutionSupportPercentage" double precision, "resourcesTarget" json, "damageRequirement" double precision, "attackerBonus" double precision NOT NULL DEFAULT '1', "defenderBonus" double precision NOT NULL DEFAULT '1', "battleEvents" json NOT NULL DEFAULT '[]', "participants" json NOT NULL DEFAULT '{}', "cooldownUntil" TIMESTAMP, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "attackerStateId" uuid, "attackerRegionId" uuid, "defenderStateId" uuid, "defenderRegionId" uuid, "targetRegionId" uuid, "declaredById" integer, CONSTRAINT "PK_9e70fb6a71074714eb0c2959aec" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "region" ADD "stateId" uuid`);
    await queryRunner.query(
      `ALTER TYPE "public"."region_status_enum" RENAME TO "region_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."region_status_enum" AS ENUM('independent', 'state_member', 'autonomy')`,
    );
    await queryRunner.query(
      `ALTER TABLE "region" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "region" ALTER COLUMN "status" TYPE "public"."region_status_enum" USING "status"::"text"::"public"."region_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "region" ALTER COLUMN "status" SET DEFAULT 'independent'`,
    );
    await queryRunner.query(`DROP TYPE "public"."region_status_enum_old"`);
    await queryRunner.query(
      `ALTER TABLE "region" ADD CONSTRAINT "FK_4df2db3ab1b36697d454b52def2" FOREIGN KEY ("stateId") REFERENCES "state"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "state" ADD CONSTRAINT "FK_e4249b78335fe5c7b2057d94cb4" FOREIGN KEY ("leaderId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ADD CONSTRAINT "FK_826938e5ca05f3c40be49e524ca" FOREIGN KEY ("attackerStateId") REFERENCES "state"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ADD CONSTRAINT "FK_960e83411a78178e38a9628be46" FOREIGN KEY ("attackerRegionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ADD CONSTRAINT "FK_76ec9b0a256e36931e90888abc1" FOREIGN KEY ("defenderStateId") REFERENCES "state"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ADD CONSTRAINT "FK_d61b3a2cc468e5bdb7d8d5b215e" FOREIGN KEY ("defenderRegionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ADD CONSTRAINT "FK_0300d306c13e0c7fb6fee82b648" FOREIGN KEY ("targetRegionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ADD CONSTRAINT "FK_6df080703140b3b647b4ed16fd8" FOREIGN KEY ("declaredById") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "war" DROP CONSTRAINT "FK_6df080703140b3b647b4ed16fd8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" DROP CONSTRAINT "FK_0300d306c13e0c7fb6fee82b648"`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" DROP CONSTRAINT "FK_d61b3a2cc468e5bdb7d8d5b215e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" DROP CONSTRAINT "FK_76ec9b0a256e36931e90888abc1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" DROP CONSTRAINT "FK_960e83411a78178e38a9628be46"`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" DROP CONSTRAINT "FK_826938e5ca05f3c40be49e524ca"`,
    );
    await queryRunner.query(
      `ALTER TABLE "state" DROP CONSTRAINT "FK_e4249b78335fe5c7b2057d94cb4"`,
    );
    await queryRunner.query(
      `ALTER TABLE "region" DROP CONSTRAINT "FK_4df2db3ab1b36697d454b52def2"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."region_status_enum_old" AS ENUM('independent', 'autonomy')`,
    );
    await queryRunner.query(
      `ALTER TABLE "region" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "region" ALTER COLUMN "status" TYPE "public"."region_status_enum_old" USING "status"::"text"::"public"."region_status_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "region" ALTER COLUMN "status" SET DEFAULT 'independent'`,
    );
    await queryRunner.query(`DROP TYPE "public"."region_status_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."region_status_enum_old" RENAME TO "region_status_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "region" DROP COLUMN "stateId"`);
    await queryRunner.query(`DROP TABLE "war"`);
    await queryRunner.query(`DROP TYPE "public"."war_wartarget_enum"`);
    await queryRunner.query(`DROP TYPE "public"."war_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."war_wartype_enum"`);
    await queryRunner.query(`DROP TABLE "state"`);
  }
}
