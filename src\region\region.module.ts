import { TypeOrmModule } from '@nestjs/typeorm';
import { Region } from './entity/region.entity';
import { RegionController } from './region.controller';
import { RegionService } from './region.service';
import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { User } from '../user/entity/user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Region, User]), HttpModule],
  controllers: [RegionController],
  providers: [RegionService],
  exports: [RegionService],
})
export class RegionModule {}
