import { Injectable, Logger } from '@nestjs/common';
import { BullMQAutoActionService } from '../shared/bullmq-auto-action.service';
import { AutoMode } from 'src/user/enums/auto-mode.enum';

@Injectable()
export class WorkAutoService {
  private readonly logger = new Logger(WorkAutoService.name);

  constructor(
    private readonly bullMQAutoActionService: BullMQAutoActionService,
  ) {}

  /**
   * Start auto work for a user at a factory
   * @param userId User ID
   * @param factoryId Factory ID
   */
  async startAutoWork(userId: number, factoryId: string): Promise<void> {
    this.logger.log(`Starting auto work for user ${userId} at factory ${factoryId}`);

    // Start the auto action using BullMQ
    await this.bullMQAutoActionService.startAutoAction(userId, factoryId, AutoMode.WORK);

    this.logger.log(`Auto work started for user ${userId} at factory ${factoryId}`);
  }

  /**
   * Stop auto work for a user at a factory
   * @param userId User ID
   * @param factoryId Factory ID
   */
  async stopAutoWork(userId: number, factoryId: string): Promise<void> {
    this.logger.log(`Stopping auto work for user ${userId} at factory ${factoryId}`);

    // Stop the auto action using BullMQ
    await this.bullMQAutoActionService.stopAutoAction(userId, factoryId, AutoMode.WORK);

    this.logger.log(`Auto work stopped for user ${userId} at factory ${factoryId}`);
  }
}
