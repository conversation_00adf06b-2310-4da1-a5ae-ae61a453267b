import { ApiProperty } from '@nestjs/swagger';
import { ResourcesCapturedDto } from './resources-captured.dto';

export class WarOutcomeDto {
  @ApiProperty({
    description: 'Description of the war outcome',
    example: 'Republic of Atlantis successfully conquered Western Pacifica.',
  })
  description: string;

  @ApiProperty({
    description: 'Whether territory changed hands',
    example: true,
    required: false,
  })
  territoryChanged?: boolean;

  @ApiProperty({
    description: 'Name of the region transferred (if applicable)',
    example: 'Western Pacifica',
    required: false,
  })
  regionTransferred?: string;

  @ApiProperty({
    description: 'Resources captured (if applicable)',
    type: ResourcesCapturedDto,
    required: false,
  })
  resourcesCaptured?: ResourcesCapturedDto;

  @ApiProperty({
    description: 'Whether leadership changed (for revolutions)',
    example: true,
    required: false,
  })
  leadershipChanged?: boolean;

  @ApiProperty({
    description: 'Name of the new leader (if applicable)',
    example: 'GeneralSupreme',
    required: false,
  })
  newLeader?: string;
}
