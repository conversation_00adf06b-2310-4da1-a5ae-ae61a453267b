// src/region/region.service.ts
import { HttpException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { CreateRegionDto } from './dto/create-region.dto';
import { UpdateRegionDto } from './dto/update-region.dto';
import { Region, RegionStatus } from './entity/region.entity';
import { HttpService } from '@nestjs/axios';
import { catchError, lastValueFrom, map } from 'rxjs';
import { User } from '../user/entity/user.entity';

@Injectable()
export class RegionService {
  constructor(
    @InjectRepository(Region)
    private regionRepository: Repository<Region>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private httpService: HttpService,
  ) {}

  async create(createRegionDto: CreateRegionDto): Promise<Region> {
    const region = this.regionRepository.create(createRegionDto);
    return this.regionRepository.save(region);
  }

  async findAll(): Promise<Region[]> {
    const regions = await this.regionRepository.find({
      order: { name: 'ASC' },
      relations: ['state'],
    });

    // Process regions to add borders and population
    const processedRegions = await Promise.all(
      regions.map(async (region) => {
        const regionWithBorders = await this.addBordersWithNames(region);
        return this.addPopulation(regionWithBorders);
      }),
    );

    return processedRegions;
  }

  async findAllCount(): Promise<number> {
    return await this.regionRepository.count();
  }

  async findById(id: string): Promise<Region> {
    // Fetch the region with user count in a single query
    const region = await this.regionRepository
      .createQueryBuilder('region')
      .leftJoin('region.users', 'user')
      .leftJoin('region.state', 'state')
      .select([
        'region',
        'user.id',
        'user.username',
        'user.isPremium',
        'user.level',
        'user.avatarUrl',
        'state',
      ])
      .where('region.id = :id', { id })
      .groupBy('region.id, user.id,state.id')
      .getOne();

    if (!region) {
      throw new NotFoundException('Region not found');
    }

    // Add borders with names
    // const regionWithBorders = await this.addBordersWithNames(region);
    return region;
  }

  /**
   * Calculates and adds the population count (number of users) to a region
   * This method is public so it can be used by other services
   */
  public async addPopulation(region: Region): Promise<Region> {
    const userCount = await this.userRepository.count({
      where: { region: { id: region.id } },
    });

    region.population = userCount;
    return region;
  }

  async update(id: string, updateRegionDto: UpdateRegionDto): Promise<Region> {
    await this.regionRepository.update(id, updateRegionDto);
    return this.findById(id);
  }

  async remove(id: string): Promise<void> {
    await this.regionRepository.delete(id);
  }

  async seedRegions(): Promise<{ updated: number; unmatched: number }> {
    try {
      const regions = await this.regionRepository.find();
      const response = await lastValueFrom(
        this.httpService.get('https://restcountries.com/v3.1/all').pipe(
          map((res) => res.data),
          catchError((e) => {
            throw new HttpException(
              e.response?.data?.errorMessage || 'Error fetching countries',
              e.response?.data?.statusCode || 500,
            );
          }),
        ),
      );

      let updated = 0;
      let unmatched = 0;

      for (const country of response) {
        const name = country.name?.common;
        const latlng = country.latlng || [];
        const countryCode = country.cca3 || country.cioc || null;

        if (!name || latlng.length < 2) continue;

        const region = regions.find(
          (r) => r.name === name || r.countryCode === countryCode,
        );

        if (region) {
          region.latitude = latlng[0];
          region.longitude = latlng[1];
          await this.regionRepository.save(region);
          updated++;
        } else {
          unmatched++;
          console.log(`Unmatched country: ${name} (${countryCode})`);
        }
      }

      return { updated, unmatched };
    } catch (error) {
      throw error;
    }
  }

  async searchByName(name: string): Promise<Region[]> {
    const regions = await this.regionRepository
      .createQueryBuilder('region')
      .where('LOWER(region.name) LIKE :name', {
        name: `%${name.toLowerCase()}%`,
      })
      .orderBy('region.name', 'ASC')
      .getMany();

    // Add population counts to each region
    return Promise.all(
      regions.map(async (region) => {
        return this.addPopulation(region);
      }),
    );
  }

  /**
   * This helper method takes a Region and returns an object that includes a new field 'bordersWithNames'
   * where each country code in the bordersWith array is replaced with the corresponding region name.
   */
  public async addBordersWithNames(region: Region): Promise<any> {
    let bordersWithNames: string[] = [];
    if (region.bordersWith && region.bordersWith.length > 0) {
      // Look up all regions with a matching countryCode in region.bordersWith array.
      const borderingRegions = await this.regionRepository.find({
        where: { countryCode: In(region.bordersWith) },
      });
      bordersWithNames = borderingRegions.map((r) => r.name);
    }
    // Return the region object with an additional field bordersWithNames.
    // Preserve the population field if it exists
    return {
      ...region,
      bordersWithNames,
      population: region.population,
    };
  }
}
