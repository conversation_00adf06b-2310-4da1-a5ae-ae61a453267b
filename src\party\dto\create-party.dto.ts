import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class CreatePartyDto {
  @ApiProperty({ description: 'Party name', example: 'Rebels' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Party description',
    example: 'A group of rebels',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Region ID where the party is formed',
    example: 'uuid-region',
  })
  @IsString()
  @IsNotEmpty()
  regionId: string;
}
