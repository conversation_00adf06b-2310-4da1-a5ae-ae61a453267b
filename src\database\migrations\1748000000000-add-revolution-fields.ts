import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRevolutionFields1748000000000 implements MigrationInterface {
  name = 'AddRevolutionFields1748000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add hasOpenBorders field to state table
    await queryRunner.query(
      `ALTER TABLE "state" ADD "hasOpenBorders" boolean NOT NULL DEFAULT false`,
    );

    // Add lastRevolutionAt field to region table
    await queryRunner.query(
      `ALTER TABLE "region" ADD "lastRevolutionAt" TIMESTAMP`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the added fields
    await queryRunner.query(`ALTER TABLE "region" DROP COLUMN "lastRevolutionAt"`);
    await queryRunner.query(`ALTER TABLE "state" DROP COLUMN "hasOpenBorders"`);
  }
}
