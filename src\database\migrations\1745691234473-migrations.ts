import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1745691234473 implements MigrationInterface {
  name = 'Migrations1745691234473';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."user_subscriptionstatus_enum" RENAME TO "user_subscriptionstatus_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."user_subscriptionstatus_enum" AS ENUM('active', 'inactive', 'past_due', 'canceled', 'unpaid', 'trialing', 'cancel_at_period_end')`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "subscriptionStatus" TYPE "public"."user_subscriptionstatus_enum" USING "subscriptionStatus"::"text"::"public"."user_subscriptionstatus_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."user_subscriptionstatus_enum_old"`,
    );
  }

  public async down(queryRunner: Query<PERSON>unner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."user_subscriptionstatus_enum_old" AS ENUM('active', 'inactive', 'past_due', 'canceled', 'unpaid', 'trialing')`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "subscriptionStatus" TYPE "public"."user_subscriptionstatus_enum_old" USING "subscriptionStatus"::"text"::"public"."user_subscriptionstatus_enum_old"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."user_subscriptionstatus_enum"`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."user_subscriptionstatus_enum_old" RENAME TO "user_subscriptionstatus_enum"`,
    );
  }
}
