import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { WarHistory } from './entity/war-history.entity';
import { War, WarStatus, WarTarget } from './entity/war.entity';
import { User } from '../user/entity/user.entity';

@Injectable()
export class WarHistoryService {
  constructor(
    @InjectRepository(WarHistory)
    private warHistoryRepository: Repository<WarHistory>,
    @InjectRepository(War)
    private warRepository: Repository<War>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async archiveWar(war: War): Promise<WarHistory> {
    // Create a new war history record from the completed war
    const warHistory = new WarHistory();
    warHistory.warId = war.id;
    warHistory.warType = war.warType;
    warHistory.warTarget = war.warTarget;
    warHistory.declaration = war.declaration;

    // Store names instead of references to entities
    warHistory.attackerStateName = war.attackerState?.name;
    warHistory.attackerRegionName = war.attackerRegion?.name;
    warHistory.defenderStateName = war.defenderState?.name;
    warHistory.defenderRegionName = war.defenderRegion?.name;
    warHistory.targetRegionName = war.targetRegion?.name;

    warHistory.declaredByUsername = war.declaredBy?.username;
    warHistory.declaredAt = war.declaredAt;
    warHistory.startedAt = war.startedAt;
    warHistory.endedAt = war.endedAt;

    // Calculate duration in hours
    if (war.startedAt && war.endedAt) {
      const durationMs = war.endedAt.getTime() - war.startedAt.getTime();
      warHistory.durationHours = durationMs / (1000 * 60 * 60);
    }

    warHistory.finalStatus = war.status;

    // Determine the winner
    if (war.status === WarStatus.ENDED) {
      const attackerWon =
        war.attackerGroundDamage >= war.damageRequirement &&
        war.attackerGroundDamage > war.defenderGroundDamage;
      warHistory.winner = attackerWon ? 'attacker' : 'defender';
    } else {
      warHistory.winner = null;
    }

    // Store damage statistics
    warHistory.totalAttackerDamage = war.attackerGroundDamage;
    warHistory.totalDefenderDamage = war.defenderGroundDamage;

    // Calculate participant counts
    const attackerParticipants = war.participants?.attackers?.length || 0;
    const defenderParticipants = war.participants?.defenders?.length || 0;
    warHistory.attackerParticipants = attackerParticipants;
    warHistory.defenderParticipants = defenderParticipants;
    warHistory.totalParticipants = attackerParticipants + defenderParticipants;

    // Calculate total energy spent (this is an estimate as we don't store energy spent per participant)
    // In a real implementation, you would track energy spent per participant
    warHistory.totalEnergySpent = Math.round(
      (warHistory.totalAttackerDamage + warHistory.totalDefenderDamage) / 100,
    );

    // Store resources captured (for resource wars)
    if (war.warTarget === WarTarget.RESOURCES && war.resourcesTarget) {
      warHistory.resourcesCaptured = {
        gold:
          war.resourcesTarget.type === 'gold'
            ? war.resourcesTarget.amount
            : undefined,
        oil:
          war.resourcesTarget.type === 'oil'
            ? war.resourcesTarget.amount
            : undefined,
        ore:
          war.resourcesTarget.type === 'ore'
            ? war.resourcesTarget.amount
            : undefined,
        uranium:
          war.resourcesTarget.type === 'uranium'
            ? war.resourcesTarget.amount
            : undefined,
        diamonds:
          war.resourcesTarget.type === 'diamonds'
            ? war.resourcesTarget.amount
            : undefined,
      };
    }

    // Get top damage dealers
    const topDamageDealers = await this.getTopDamageDealers(war);
    warHistory.topDamageDealers = topDamageDealers;

    // Extract key events from battle events
    warHistory.keyEvents = this.extractKeyEvents(war);

    return this.warHistoryRepository.save(warHistory);
  }

  private async getTopDamageDealers(war: War): Promise<any[]> {
    const topDealers: Array<{
      userId: number;
      username: string;
      damage: number;
      side: 'attacker' | 'defender';
    }> = [];

    // Process attackers
    const attackers = war.participants?.attackers || [];
    for (const attacker of attackers) {
      const user = await this.userRepository.findOne({
        where: { id: attacker.userId },
      });
      if (user) {
        topDealers.push({
          userId: user.id,
          username: user.username,
          damage: attacker.damage,
          side: 'attacker',
        });
      }
    }

    // Process defenders
    const defenders = war.participants?.defenders || [];
    for (const defender of defenders) {
      const user = await this.userRepository.findOne({
        where: { id: defender.userId },
      });
      if (user) {
        topDealers.push({
          userId: user.id,
          username: user.username,
          damage: defender.damage,
          side: 'defender',
        });
      }
    }

    // Sort by damage and take top 5
    return topDealers.sort((a, b) => b.damage - a.damage).slice(0, 5);
  }

  private extractKeyEvents(war: War): any[] {
    const keyEvents: Array<{
      timestamp: Date;
      description: string;
      eventType: string;
    }> = [];

    // Add war declaration
    if (war.declaredAt) {
      keyEvents.push({
        timestamp: war.declaredAt,
        description: `War declared by ${war.declaredBy?.username}`,
        eventType: 'declaration',
      });
    }

    // Add war start
    if (war.startedAt) {
      keyEvents.push({
        timestamp: war.startedAt,
        description: 'War officially began',
        eventType: 'start',
      });
    }

    // Add sea phase events
    if (war.seaPhaseStartedAt) {
      keyEvents.push({
        timestamp: war.seaPhaseStartedAt,
        description: 'Sea phase began',
        eventType: 'sea_phase_start',
      });
    }

    if (war.seaPhaseEndedAt) {
      const attackerWonSea = war.attackerSeaDamage > war.defenderSeaDamage;
      keyEvents.push({
        timestamp: war.seaPhaseEndedAt,
        description: `Sea phase ended. ${attackerWonSea ? 'Attackers' : 'Defenders'} won the sea phase.`,
        eventType: 'sea_phase_end',
      });
    }

    // Add ground phase events
    if (war.groundPhaseStartedAt) {
      keyEvents.push({
        timestamp: war.groundPhaseStartedAt,
        description: 'Ground phase began',
        eventType: 'ground_phase_start',
      });
    }

    // Add war end
    if (war.endedAt) {
      const attackerWon =
        war.attackerGroundDamage >= war.damageRequirement &&
        war.attackerGroundDamage > war.defenderGroundDamage;

      keyEvents.push({
        timestamp: war.endedAt,
        description: `War ended. ${attackerWon ? 'Attackers' : 'Defenders'} emerged victorious.`,
        eventType: 'end',
      });
    }

    // Add significant battle events (high damage)
    if (war.battleEvents) {
      const significantDamageThreshold = war.damageRequirement * 0.2; // 20% of required damage

      for (const event of war.battleEvents) {
        if (event.damage && event.damage >= significantDamageThreshold) {
          keyEvents.push({
            timestamp: event.timestamp,
            description: event.description,
            eventType: 'significant_damage',
          });
        }
      }
    }

    // Sort by timestamp
    return keyEvents.sort(
      (a, b) => a.timestamp.getTime() - b.timestamp.getTime(),
    );
  }

  async findAllWarHistory(): Promise<WarHistory[]> {
    return this.warHistoryRepository.find({
      order: { endedAt: 'DESC' },
    });
  }

  async findWarHistoryById(id: string): Promise<WarHistory> {
    const result = await this.warHistoryRepository.findOne({ where: { id } });
    if (!result) {
      throw new Error(`War history with ID ${id} not found`);
    }
    return result;
  }

  async findWarHistoryByOriginalWarId(warId: string): Promise<WarHistory> {
    const result = await this.warHistoryRepository.findOne({
      where: { warId },
    });
    if (!result) {
      throw new Error(`War history with war ID ${warId} not found`);
    }
    return result;
  }

  async findWarHistoryByDateRange(
    startDate: Date,
    endDate: Date,
  ): Promise<WarHistory[]> {
    return this.warHistoryRepository.find({
      where: {
        endedAt: Between(startDate, endDate),
      },
      order: { endedAt: 'DESC' },
    });
  }

  async findWarHistoryByRegion(regionName: string): Promise<WarHistory[]> {
    return this.warHistoryRepository.find({
      where: [
        { attackerRegionName: regionName },
        { defenderRegionName: regionName },
        { targetRegionName: regionName },
      ],
      order: { endedAt: 'DESC' },
    });
  }

  async findWarHistoryByState(stateName: string): Promise<WarHistory[]> {
    return this.warHistoryRepository.find({
      where: [
        { attackerStateName: stateName },
        { defenderStateName: stateName },
      ],
      order: { endedAt: 'DESC' },
    });
  }

  async findWarHistoryByUser(username: string): Promise<WarHistory[]> {
    // This is a simplified approach. In a real implementation, you would need to check
    // if the user was a participant in the war, not just the declarer
    return this.warHistoryRepository.find({
      where: { declaredByUsername: username },
      order: { endedAt: 'DESC' },
    });
  }

  async getWarHistoryStatistics(): Promise<any> {
    const allWarHistory = await this.warHistoryRepository.find();

    // Calculate various statistics
    const totalWars = allWarHistory.length;
    const attackerWins = allWarHistory.filter(
      (w) => w.winner === 'attacker',
    ).length;
    const defenderWins = allWarHistory.filter(
      (w) => w.winner === 'defender',
    ).length;

    // Calculate average war duration
    let totalDuration = 0;
    let warsWithDuration = 0;

    for (const war of allWarHistory) {
      if (war.durationHours) {
        totalDuration += war.durationHours;
        warsWithDuration++;
      }
    }

    const averageDuration =
      warsWithDuration > 0 ? totalDuration / warsWithDuration : 0;

    // Count wars by type
    const warsByType = {};
    for (const war of allWarHistory) {
      warsByType[war.warType] = (warsByType[war.warType] || 0) + 1;
    }

    // Count wars by target
    const warsByTarget = {};
    for (const war of allWarHistory) {
      warsByTarget[war.warTarget] = (warsByTarget[war.warTarget] || 0) + 1;
    }

    // Find most active regions
    const regionWarCounts = {};

    for (const war of allWarHistory) {
      if (war.attackerRegionName) {
        regionWarCounts[war.attackerRegionName] =
          (regionWarCounts[war.attackerRegionName] || 0) + 1;
      }

      if (war.defenderRegionName) {
        regionWarCounts[war.defenderRegionName] =
          (regionWarCounts[war.defenderRegionName] || 0) + 1;
      }

      if (war.targetRegionName) {
        regionWarCounts[war.targetRegionName] =
          (regionWarCounts[war.targetRegionName] || 0) + 1;
      }
    }

    const mostActiveRegions = Object.entries(regionWarCounts)
      .map(([regionName, count]) => ({ regionName, warCount: count }))
      .sort((a, b) => (b.warCount as number) - (a.warCount as number))
      .slice(0, 5);

    // Find most active states
    const stateWarCounts = {};

    for (const war of allWarHistory) {
      if (war.attackerStateName) {
        stateWarCounts[war.attackerStateName] =
          (stateWarCounts[war.attackerStateName] || 0) + 1;
      }

      if (war.defenderStateName) {
        stateWarCounts[war.defenderStateName] =
          (stateWarCounts[war.defenderStateName] || 0) + 1;
      }
    }

    const mostActiveStates = Object.entries(stateWarCounts)
      .map(([stateName, count]) => ({ stateName, warCount: count }))
      .sort((a, b) => (b.warCount as number) - (a.warCount as number))
      .slice(0, 5);

    // Calculate total damage and participants
    let totalAttackerDamage = 0;
    let totalDefenderDamage = 0;
    let totalParticipants = 0;

    for (const war of allWarHistory) {
      totalAttackerDamage += war.totalAttackerDamage || 0;
      totalDefenderDamage += war.totalDefenderDamage || 0;
      totalParticipants += war.totalParticipants || 0;
    }

    return {
      totalWars,
      attackerWins,
      defenderWins,
      winRate: {
        attacker: totalWars > 0 ? (attackerWins / totalWars) * 100 : 0,
        defender: totalWars > 0 ? (defenderWins / totalWars) * 100 : 0,
      },
      averageDuration,
      warsByType,
      warsByTarget,
      mostActiveRegions,
      mostActiveStates,
      damageStatistics: {
        totalAttackerDamage,
        totalDefenderDamage,
        ratio:
          totalDefenderDamage > 0
            ? totalAttackerDamage / totalDefenderDamage
            : 0,
      },
      participationStatistics: {
        totalParticipants,
        averagePerWar: totalWars > 0 ? totalParticipants / totalWars : 0,
      },
    };
  }
}
