import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { UserService } from '../user/user.service';
import { User } from '../user/entity/user.entity';
import { StripeService } from './stripe.service';
import {
  PaymentTransaction,
  PaymentStatus,
  PaymentType,
} from './entity/payment-transaction.entity';
import { GoldPurchase } from './entity/gold-purchase.entity';
import {
  CreatePaymentIntentDto,
  GoldPackage,
} from './dto/create-payment-intent.dto';
import { PaymentIntentResponseDto } from './dto/payment-intent-response.dto';
import {
  CreateCheckoutSessionDto,
  PremiumPlan,
} from './dto/create-checkout-session.dto';
import { CheckoutSessionResponseDto } from './dto/checkout-session-response.dto';
import { SubscriptionStatusResponseDto } from './dto/subscription-status-response.dto';
import { MailService } from '../mail/mail.service';
import Stripe from 'stripe';

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);
  private readonly goldPackagePrices = {
    [GoldPackage.SMALL]: { amount: 1100, price: 500 }, // 1100 gold for $5
    [GoldPackage.MEDIUM]: { amount: 3500, price: 1000 }, // 3500 gold for $10
    [GoldPackage.LARGE]: { amount: 12000, price: 2500 }, // 12000 gold for $25
    [GoldPackage.EXTRA_LARGE]: { amount: 30000, price: 5000 }, // 30000 gold for $50
    [GoldPackage.CUSTOM]: { amountPerDollar: 100 }, // 100 gold per $1
  };

  private readonly premiumPlanPriceIds = {
    [PremiumPlan.MONTHLY]: process.env.STRIPE_PREMIUM_MONTHLY_PRICE_ID,
    [PremiumPlan.SEMIANNUAL]: process.env.STRIPE_PREMIUM_SEMIANNUAL_PRICE_ID,
    [PremiumPlan.YEARLY]: process.env.STRIPE_PREMIUM_YEARLY_PRICE_ID,
  };

  private readonly goldPackagePriceIds = {
    [GoldPackage.SMALL]: process.env.STRIPE_GOLD_SMALL_PRICE_ID,
    [GoldPackage.MEDIUM]: process.env.STRIPE_GOLD_MEDIUM_PRICE_ID,
    [GoldPackage.LARGE]: process.env.STRIPE_GOLD_LARGE_PRICE_ID,
    [GoldPackage.EXTRA_LARGE]: process.env.STRIPE_GOLD_EXTRA_LARGE_PRICE_ID,
  };

  constructor(
    @InjectRepository(PaymentTransaction)
    private paymentTransactionRepository: Repository<PaymentTransaction>,
    @InjectRepository(GoldPurchase)
    private goldPurchaseRepository: Repository<GoldPurchase>,
    private userService: UserService,
    private stripeService: StripeService,
    private configService: ConfigService,
    private mailService: MailService,
  ) {}

  async createPaymentIntent(
    userId: number,
    createPaymentIntentDto: CreatePaymentIntentDto,
  ): Promise<PaymentIntentResponseDto> {
    const user = await this.userService.findOne(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const {
      package: goldPackage,
      goldAmount,
      currency = 'usd',
    } = createPaymentIntentDto;
    let amount: number;
    let price: number;

    if (goldPackage === GoldPackage.CUSTOM) {
      if (!goldAmount || goldAmount < 100) {
        throw new BadRequestException(
          'Gold amount must be at least 100 for custom package',
        );
      }
      // Calculate price based on gold amount (100 gold per $1)
      price = Math.ceil(
        (goldAmount /
          this.goldPackagePrices[GoldPackage.CUSTOM].amountPerDollar) *
          100,
      );
      amount = goldAmount;
    } else {
      // Use predefined package
      amount = this.goldPackagePrices[goldPackage].amount;
      price = this.goldPackagePrices[goldPackage].price;
    }

    // Create metadata for the payment intent
    const metadata = {
      userId: user.id.toString(),
      goldAmount: amount.toString(),
      packageType: goldPackage,
    };

    // Create a payment intent with Stripe
    const paymentIntent = await this.stripeService.createPaymentIntent(
      price,
      currency,
      metadata,
    );

    // Save the payment transaction in our database
    const transaction = this.paymentTransactionRepository.create({
      amount: price,
      currency,
      status: PaymentStatus.PENDING,
      type: PaymentType.GOLD_PURCHASE,
      stripePaymentIntentId: paymentIntent.id,
      user,
      userId: user.id,
      metadata: {
        goldAmount: amount,
        packageType: goldPackage,
      },
    });

    await this.paymentTransactionRepository.save(transaction);

    return {
      clientSecret: paymentIntent.client_secret || '',
      paymentIntentId: paymentIntent.id,
      goldAmount: amount,
      amount: price,
      currency,
    };
  }

  async createCheckoutSession(
    userId: number,
    createCheckoutSessionDto: CreateCheckoutSessionDto,
  ): Promise<CheckoutSessionResponseDto> {
    const user = await this.userService.findOne(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const { plan, successUrl, cancelUrl } = createCheckoutSessionDto;
    const priceId = this.premiumPlanPriceIds[plan];

    if (!priceId) {
      throw new BadRequestException(`Invalid premium plan: ${plan}`);
    }

    // Create or retrieve a Stripe customer
    let customerId = user.stripeCustomerId;
    let customerExists = false;

    // Check if customer exists in Stripe if we have an ID
    if (customerId) {
      try {
        await this.stripeService.getCustomer(customerId);
        customerExists = true;
        this.logger.log(`Using existing Stripe customer: ${customerId}`);
      } catch (error) {
        this.logger.warn(
          `Stripe customer ${customerId} not found: ${error.message}`,
        );
        // Customer doesn't exist in Stripe, we'll create a new one
        customerId = undefined;
        user.stripeCustomerId = undefined;
      }
    }

    // Create a new customer if needed
    if (!customerExists) {
      try {
        const customer = await this.stripeService.createCustomer(
          user.email,
          user.username,
        );
        customerId = customer.id;
        this.logger.log(`Created new Stripe customer: ${customerId}`);

        // Update user with new Stripe customer ID
        user.stripeCustomerId = customerId;
        await this.userService.update(user.id, {
          stripeCustomerId: customerId,
        });
      } catch (error) {
        this.logger.error(
          `Error creating Stripe customer: ${error.message}`,
          error.stack,
        );
        throw error;
      }
    }

    // Create metadata for the checkout session
    const metadata = {
      userId: user.id.toString(),
      plan,
    };

    // Default URLs if not provided
    const defaultSuccessUrl = `${this.configService.get('CLIENT_URL')}/premium/success`;
    const defaultCancelUrl = `${this.configService.get('CLIENT_URL')}/premium/cancel`;

    // Ensure we have a valid customer ID at this point
    if (!customerId) {
      throw new Error(
        'Failed to create or retrieve a valid Stripe customer ID',
      );
    }

    // Create a checkout session with Stripe
    const session = await this.stripeService.createCheckoutSession(
      priceId,
      customerId,
      metadata,
      successUrl || defaultSuccessUrl,
      cancelUrl || defaultCancelUrl,
    );

    // Save the payment transaction in our database
    const transaction = this.paymentTransactionRepository.create({
      amount: 0, // Will be updated when the webhook is received
      currency: 'usd',
      status: PaymentStatus.PENDING,
      type: PaymentType.PREMIUM_SUBSCRIPTION,
      stripeSessionId: session.id,
      stripeCustomerId: customerId, // Store the customer ID for easier lookup
      user,
      userId: user.id,
      metadata: {
        plan,
        userId: user.id.toString(), // Duplicate userId in metadata for redundancy
      },
    });

    this.logger.log(
      `Created payment transaction: ${transaction.id} for user ${user.id} with session ${session.id} and customer ${customerId}`,
    );

    await this.paymentTransactionRepository.save(transaction);

    return {
      sessionId: session.id,
      url: session.url || '',
    };
  }

  async verifySubscription(
    userId: number,
  ): Promise<SubscriptionStatusResponseDto> {
    const user = await this.userService.findOne(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // If user doesn't have a subscription ID, they're not premium
    if (!user.stripeSubscriptionId) {
      return {
        isPremium: false,
        status: null,
        expiresAt: null,
      };
    }

    // If subscription status is not active, they're not premium
    if (
      user.subscriptionStatus !== 'active' &&
      user.subscriptionStatus !== 'trialing'
    ) {
      return {
        isPremium: false,
        status: user.subscriptionStatus || null,
        expiresAt: user.premiumExpiresAt || null,
      };
    }

    // Check if premium has expired
    if (user.premiumExpiresAt && new Date() > user.premiumExpiresAt) {
      return {
        isPremium: false,
        status: 'expired',
        expiresAt: user.premiumExpiresAt,
      };
    }

    return {
      isPremium: user.isPremium,
      status: user.subscriptionStatus || null,
      expiresAt: user.premiumExpiresAt || null,
    };
  }

  async handlePaymentIntentSucceeded(
    paymentIntent: Stripe.PaymentIntent,
  ): Promise<void> {
    const { metadata } = paymentIntent;
    if (!metadata || !metadata.userId || !metadata.goldAmount) {
      this.logger.error('Missing metadata in payment intent', paymentIntent.id);
      return;
    }

    const userId = parseInt(metadata.userId, 10);
    const goldAmount = parseInt(metadata.goldAmount, 10);

    // Find the transaction
    const transaction = await this.paymentTransactionRepository.findOne({
      where: { stripePaymentIntentId: paymentIntent.id },
    });

    if (!transaction) {
      this.logger.error(
        `Transaction not found for payment intent ${paymentIntent.id}`,
      );
      return;
    }

    // Update transaction status
    transaction.status = PaymentStatus.SUCCEEDED;
    await this.paymentTransactionRepository.save(transaction);

    // Find the user
    const user = await this.userService.findOne(userId);
    if (!user) {
      this.logger.error(
        `User not found for payment intent ${paymentIntent.id}`,
      );
      return;
    }

    // Create gold purchase record
    const goldPurchase = this.goldPurchaseRepository.create({
      goldAmount,
      realMoneyAmount: paymentIntent.amount,
      currency: paymentIntent.currency,
      user,
      userId: user.id,
      paymentTransaction: transaction,
      paymentTransactionId: transaction.id,
    });

    await this.goldPurchaseRepository.save(goldPurchase);

    // Update user's gold balance
    user.gold += goldAmount;
    await this.userService.update(user.id, { gold: user.gold });

    // Send confirmation email
    try {
      await this.mailService.sendMail({
        to: user.email,
        subject: 'Gold Purchase Confirmation',
        template: 'gold-purchase-confirmation',
        context: {
          username: user.username,
          goldAmount,
          amount: (paymentIntent.amount / 100).toFixed(2),
          currency: paymentIntent.currency.toUpperCase(),
          date: new Date().toLocaleDateString(),
          gameUrl:
            this.configService.get('CLIENT_URL') || 'http://localhost:3000',
          currentYear: new Date().getFullYear(),
        },
      });
      this.logger.log(`Gold purchase confirmation email sent to ${user.email}`);
    } catch (error) {
      this.logger.error(
        `Error sending gold purchase confirmation email: ${error.message}`,
        error.stack,
      );
    }
  }

  async handleGoldCheckoutCompleted(
    session: Stripe.Checkout.Session,
  ): Promise<void> {
    this.logger.log(`Processing gold checkout session: ${session.id}`);

    const { metadata } = session;
    if (!metadata || !metadata.userId || !metadata.goldAmount) {
      this.logger.error(`Missing metadata in checkout session ${session.id}`);
      return;
    }

    const userId = parseInt(metadata.userId, 10);
    const goldAmount = parseInt(metadata.goldAmount, 10);

    // Find the transaction by session ID
    const transaction = await this.paymentTransactionRepository.findOne({
      where: { stripeSessionId: session.id },
    });

    if (!transaction) {
      this.logger.error(
        `Transaction not found for checkout session ${session.id}`,
      );

      // If we can't find by session ID, try to find by payment intent ID if available
      if (session.payment_intent) {
        this.logger.log(
          `Trying to find transaction by payment intent ID: ${session.payment_intent}`,
        );
        const transactionByPaymentIntent =
          await this.paymentTransactionRepository.findOne({
            where: { stripePaymentIntentId: session.payment_intent as string },
          });

        if (transactionByPaymentIntent) {
          this.logger.log(
            `Found transaction by payment intent ID: ${transactionByPaymentIntent.id}`,
          );
          // Update transaction status
          transactionByPaymentIntent.status = PaymentStatus.SUCCEEDED;
          await this.paymentTransactionRepository.save(
            transactionByPaymentIntent,
          );

          // Process the gold purchase
          await this.processGoldPurchase(
            userId,
            goldAmount,
            transactionByPaymentIntent,
            session,
          );
          return;
        }

        // If we still can't find the transaction by payment intent ID, try to get the payment intent details
        try {
          const paymentIntent = await this.stripeService.getPaymentIntent(
            session.payment_intent as string,
          );
          if (
            paymentIntent &&
            paymentIntent.metadata &&
            paymentIntent.metadata.userId
          ) {
            // Use the metadata from the payment intent if available
            const piUserId = parseInt(paymentIntent.metadata.userId, 10);
            const piGoldAmount = parseInt(
              paymentIntent.metadata.goldAmount || metadata.goldAmount,
              10,
            );

            this.logger.log(
              `Using payment intent metadata: userId=${piUserId}, goldAmount=${piGoldAmount}`,
            );

            // Create a new transaction using payment intent data
            const user = await this.userService.findOne(piUserId);
            if (user) {
              const newTransaction = this.paymentTransactionRepository.create({
                amount: paymentIntent.amount,
                currency: paymentIntent.currency,
                status: PaymentStatus.SUCCEEDED,
                type: PaymentType.GOLD_PURCHASE,
                stripeSessionId: session.id,
                stripePaymentIntentId: paymentIntent.id,
                stripeCustomerId: session.customer as string,
                user,
                userId: user.id,
                metadata: {
                  goldAmount: piGoldAmount,
                  goldPackage:
                    paymentIntent.metadata.packageType || metadata.goldPackage,
                },
              });

              await this.paymentTransactionRepository.save(newTransaction);

              // Process the gold purchase
              await this.processGoldPurchase(
                piUserId,
                piGoldAmount,
                newTransaction,
                session,
              );
              return;
            }
          }
        } catch (error) {
          this.logger.error(
            `Error retrieving payment intent: ${error.message}`,
          );
        }
      }

      // If we still can't find the transaction, create a new one
      this.logger.log(
        `Creating new transaction for checkout session ${session.id}`,
      );
      const user = await this.userService.findOne(userId);
      if (!user) {
        this.logger.error(`User not found for checkout session ${session.id}`);
        return;
      }

      const newTransaction = this.paymentTransactionRepository.create({
        amount: session.amount_total || 0,
        currency: session.currency || 'usd',
        status: PaymentStatus.SUCCEEDED,
        type: PaymentType.GOLD_PURCHASE,
        stripeSessionId: session.id,
        stripePaymentIntentId: session.payment_intent as string,
        stripeCustomerId: session.customer as string,
        user,
        userId: user.id,
        metadata: {
          goldAmount,
          goldPackage: metadata.goldPackage,
        },
      });

      await this.paymentTransactionRepository.save(newTransaction);

      // Process the gold purchase
      await this.processGoldPurchase(
        userId,
        goldAmount,
        newTransaction,
        session,
      );
      return;
    }

    // Update transaction status
    transaction.status = PaymentStatus.SUCCEEDED;
    if (session.payment_intent) {
      transaction.stripePaymentIntentId = session.payment_intent as string;
    }
    await this.paymentTransactionRepository.save(transaction);

    // Process the gold purchase
    await this.processGoldPurchase(userId, goldAmount, transaction, session);
  }

  private async processGoldPurchase(
    userId: number,
    goldAmount: number,
    transaction: PaymentTransaction,
    session: Stripe.Checkout.Session,
  ): Promise<void> {
    // Find the user
    const user = await this.userService.findOne(userId);
    if (!user) {
      this.logger.error(`User not found for userId ${userId}`);
      return;
    }

    this.logger.log(
      `Processing gold purchase for user ${user.id} (${user.username}): ${goldAmount} gold`,
    );

    // Create gold purchase record
    const goldPurchase = this.goldPurchaseRepository.create({
      goldAmount,
      realMoneyAmount: session.amount_total || 0,
      currency: session.currency || 'usd',
      user,
      userId: user.id,
      paymentTransaction: transaction,
      paymentTransactionId: transaction.id,
    });

    await this.goldPurchaseRepository.save(goldPurchase);

    // Update user's gold balance
    const previousGold = user.gold || 0;
    user.gold = previousGold + goldAmount;
    this.logger.log(
      `Updating user gold balance from ${previousGold} to ${user.gold}`,
    );
    await this.userService.update(user.id, { gold: user.gold });

    // Send confirmation email
    try {
      await this.mailService.sendMail({
        to: user.email,
        subject: 'Gold Purchase Confirmation',
        template: 'gold-purchase-confirmation',
        context: {
          username: user.username,
          goldAmount,
          amount: ((session.amount_total || 0) / 100).toFixed(2),
          currency: (session.currency || 'usd').toUpperCase(),
          date: new Date().toLocaleDateString(),
          gameUrl:
            this.configService.get('CLIENT_URL') || 'http://localhost:3000',
          currentYear: new Date().getFullYear(),
        },
      });
      this.logger.log(`Gold purchase confirmation email sent to ${user.email}`);
    } catch (error) {
      this.logger.error(
        `Error sending gold purchase confirmation email: ${error.message}`,
        error.stack,
      );
    }
  }

  async handleSubscriptionCreated(
    subscription: Stripe.Subscription,
  ): Promise<void> {
    this.logger.log(`Processing subscription created: ${subscription.id}`);

    const { metadata } = subscription;
    this.logger.log(`Subscription metadata: ${JSON.stringify(metadata)}`);

    let userId: number | null = null;

    // Try to get userId from metadata
    if (metadata && metadata.userId) {
      userId = parseInt(metadata.userId, 10);
      this.logger.log(`User ID from metadata: ${userId}`);
    } else {
      this.logger.warn(
        'Missing userId in subscription metadata, trying to find by customer ID',
      );

      // If metadata doesn't have userId, try to find the transaction by customer ID
      try {
        const transaction = await this.paymentTransactionRepository.findOne({
          where: { stripeCustomerId: subscription.customer as string },
          order: { createdAt: 'DESC' },
        });

        if (transaction) {
          userId = transaction.userId;
          this.logger.log(
            `Found userId ${userId} from transaction ${transaction.id}`,
          );
        } else {
          // If no transaction found, try to find user by stripeCustomerId directly
          try {
            const userByCustomerId =
              await this.userService.findByStripeCustomerId(
                subscription.customer as string,
              );
            userId = userByCustomerId.id;
            this.logger.log(
              `Found userId ${userId} by matching customer ID ${subscription.customer}`,
            );
          } catch (error) {
            this.logger.warn(
              `No user found with Stripe customer ID ${subscription.customer}: ${error.message}`,
            );
          }
        }
      } catch (error) {
        this.logger.error(
          `Error finding user by customer ID: ${error.message}`,
          error.stack,
        );
      }
    }

    if (!userId) {
      this.logger.error(
        `Could not determine userId for subscription ${subscription.id}`,
      );
      return;
    }

    // Find the user
    try {
      const user = await this.userService.findOne(userId);
      if (!user) {
        throw new Error(`User not found: ${userId}`);
      }

      this.logger.log(`Found user: ${user.id} (${user.username})`);

      // Calculate expiration date based on subscription period
      // Get the current period end from subscription
      const currentPeriodEnd = new Date(
        (subscription as any).current_period_end * 1000,
      );
      this.logger.log(
        `Subscription expires at: ${currentPeriodEnd.toISOString()}`,
      );

      // Update user's subscription details and energy
      user.isPremium = true;
      user.stripeSubscriptionId = subscription.id;
      user.subscriptionStatus = subscription.status;
      user.premiumExpiresAt = currentPeriodEnd;
      user.energy = 200; // Set energy to premium max value

      this.logger.log(
        `Updating user with subscription details: isPremium=${user.isPremium}, status=${user.subscriptionStatus}, energy=${user.energy}`,
      );

      try {
        await this.userService.updateSubscription(user.id, {
          isPremium: true,
          stripeSubscriptionId: subscription.id,
          subscriptionStatus: subscription.status,
          premiumExpiresAt: currentPeriodEnd,
          energy: 200, // Include energy update
        });
        this.logger.log(
          `User ${user.id} updated successfully with premium status and max energy`,
        );
      } catch (error) {
        this.logger.error(`Error updating user: ${error.message}`, error.stack);
        throw error;
      }

      // Find the transaction by customer ID and update it
      try {
        const transaction = await this.paymentTransactionRepository.findOne({
          where: { stripeCustomerId: subscription.customer as string },
          order: { createdAt: 'DESC' },
        });

        if (transaction) {
          this.logger.log(`Found transaction: ${transaction.id}`);
          transaction.status = PaymentStatus.SUCCEEDED;
          transaction.stripeSubscriptionId = subscription.id;
          const unitAmount = subscription.items.data[0].price.unit_amount;
          if (unitAmount !== null) {
            transaction.amount = unitAmount;
          }
          await this.paymentTransactionRepository.save(transaction);
          this.logger.log(`Transaction ${transaction.id} updated successfully`);
        } else {
          this.logger.warn(
            `No transaction found for customer: ${subscription.customer}`,
          );
        }
      } catch (error) {
        this.logger.error(
          `Error updating transaction: ${error.message}`,
          error.stack,
        );
      }

      // Send confirmation email
      try {
        await this.mailService.sendMail({
          to: user.email,
          subject: 'Premium Subscription Confirmation',
          template: 'premium-subscription-confirmation',
          context: {
            username: user.username,
            plan: metadata.plan,
            expiresAt: currentPeriodEnd.toLocaleDateString(),
            gameUrl:
              this.configService.get('CLIENT_URL') || 'http://localhost:3000',
            currentYear: new Date().getFullYear(),
          },
        });
        this.logger.log(`Confirmation email sent to ${user.email}`);
      } catch (error) {
        this.logger.error(
          `Error sending confirmation email: ${error.message}`,
          error.stack,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error in handleSubscriptionCreated: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async handleSubscriptionUpdated(
    subscription: Stripe.Subscription,
  ): Promise<void> {
    // Log the full subscription object for debugging
    this.logger.log(
      `Processing subscription update: ${JSON.stringify(subscription, null, 2)}`,
    );

    // Find user with this subscription ID
    let user: User;
    try {
      user = await this.userService.findByStripeSubscriptionId(subscription.id);
    } catch (error) {
      this.logger.error(
        `User not found for subscription ${subscription.id}: ${error.message}`,
      );
      return;
    }

    try {
      // Safely convert Unix timestamp to Date object
      let currentPeriodEnd: Date;
      try {
        // Get current_period_end from the subscription items
        const currentPeriodEndTimestamp =
          subscription.items.data[0]?.current_period_end;

        // Ensure current_period_end is a valid number before conversion
        if (
          typeof currentPeriodEndTimestamp === 'number' &&
          !isNaN(currentPeriodEndTimestamp) &&
          isFinite(currentPeriodEndTimestamp)
        ) {
          currentPeriodEnd = new Date(currentPeriodEndTimestamp * 1000);
          this.logger.log(
            `Current period end timestamp: ${currentPeriodEndTimestamp}, converted to: ${currentPeriodEnd.toISOString()}`,
          );
        } else {
          // If we can't get the period end from items, try to get it from cancel_at
          const cancelAtTimestamp = subscription.cancel_at;
          if (
            typeof cancelAtTimestamp === 'number' &&
            !isNaN(cancelAtTimestamp) &&
            isFinite(cancelAtTimestamp)
          ) {
            currentPeriodEnd = new Date(cancelAtTimestamp * 1000);
            this.logger.log(
              `Using cancel_at as period end: ${cancelAtTimestamp}, converted to: ${currentPeriodEnd.toISOString()}`,
            );
          } else {
            throw new Error(
              'No valid period end timestamp found in subscription data',
            );
          }
        }
      } catch (error) {
        this.logger.error(
          `Error converting current_period_end: ${error.message}`,
        );
        throw error; // Re-throw the error instead of using an incorrect fallback
      }

      // Check if the subscription is being canceled at period end
      const isCancelingAtPeriodEnd = subscription.cancel_at_period_end === true;

      this.logger.log(
        `Updating subscription for user ${user.id}: status=${subscription.status}, expires=${currentPeriodEnd.toISOString()}, cancelAtPeriodEnd=${isCancelingAtPeriodEnd}`,
      );

      await this.userService.updateSubscription(user.id, {
        isPremium:
          subscription.status === 'active' ||
          subscription.status === 'trialing',
        subscriptionStatus: isCancelingAtPeriodEnd
          ? 'cancel_at_period_end'
          : subscription.status,
        premiumExpiresAt: currentPeriodEnd,
      });

      if (isCancelingAtPeriodEnd) {
        this.logger.log(
          `User ${user.id} subscription set to cancel at period end (${currentPeriodEnd.toISOString()})`,
        );
      } else {
        this.logger.log(`User ${user.id} subscription updated successfully`);
      }
    } catch (error) {
      this.logger.error(
        `Error updating user subscription: ${error.message}`,
        error.stack,
      );
    }
  }

  async handleSubscriptionDeleted(
    subscription: Stripe.Subscription,
  ): Promise<void> {
    this.logger.log(
      `Processing subscription deletion: ${JSON.stringify(subscription, null, 2)}`,
    );

    let user: User;
    try {
      user = await this.userService.findByStripeSubscriptionId(subscription.id);
    } catch (error) {
      this.logger.error(
        `User not found for subscription ${subscription.id}: ${error.message}`,
      );
      return;
    }

    // Update user's subscription details
    try {
      await this.userService.updateSubscription(user.id, {
        isPremium: false,
        subscriptionStatus: 'canceled',
        stripeSubscriptionId: '', // Clear the subscription ID
        // Don't update premiumExpiresAt here as it should already be set correctly
      });
      this.logger.log(`User ${user.id} subscription canceled successfully`);
    } catch (error) {
      this.logger.error(
        `Error canceling user subscription: ${error.message}`,
        error.stack,
      );
    }

    // Format the expiration date safely for the email
    let expiresAtFormatted = 'immediately';
    try {
      if (user.premiumExpiresAt && user.premiumExpiresAt instanceof Date) {
        expiresAtFormatted = user.premiumExpiresAt.toLocaleDateString();
      }
    } catch (error) {
      this.logger.error(`Error formatting expiration date: ${error.message}`);
    }

    // Send cancellation email
    try {
      await this.mailService.sendMail({
        to: user.email,
        subject: 'Premium Subscription Canceled',
        template: 'premium-subscription-canceled',
        context: {
          username: user.username,
          expiresAt: expiresAtFormatted,
          gameUrl:
            this.configService.get('CLIENT_URL') || 'http://localhost:3000',
          currentYear: new Date().getFullYear(),
        },
      });
      this.logger.log(`Cancellation email sent to ${user.email}`);
    } catch (error) {
      this.logger.error(
        `Error sending cancellation email: ${error.message}`,
        error.stack,
      );
    }
  }

  async createGoldCheckoutSession(
    userId: number,
    createPaymentIntentDto: CreatePaymentIntentDto,
  ): Promise<CheckoutSessionResponseDto> {
    const user = await this.userService.findOne(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const {
      package: goldPackage,
      successUrl,
      cancelUrl,
    } = createPaymentIntentDto;

    if (goldPackage === GoldPackage.CUSTOM) {
      throw new BadRequestException(
        'Custom gold packages not supported with checkout sessions',
      );
    }

    const priceId = this.goldPackagePriceIds[goldPackage];
    if (!priceId) {
      throw new BadRequestException(`Invalid gold package: ${goldPackage}`);
    }

    // Create or retrieve Stripe customer
    let customerId = user.stripeCustomerId;
    let customerExists = false;

    if (customerId) {
      try {
        await this.stripeService.getCustomer(customerId);
        customerExists = true;
      } catch (error) {
        customerId = undefined;
        user.stripeCustomerId = undefined;
      }
    }

    if (!customerExists) {
      const customer = await this.stripeService.createCustomer(
        user.email,
        user.username,
      );
      customerId = customer.id;
      await this.userService.update(user.id, { stripeCustomerId: customerId });
    }

    const metadata = {
      userId: user.id.toString(),
      goldPackage,
      goldAmount: this.goldPackagePrices[goldPackage].amount.toString(),
    };

    const defaultSuccessUrl = `${this.configService.get('CLIENT_URL')}/gold/success`;
    const defaultCancelUrl = `${this.configService.get('CLIENT_URL')}/gold/cancel`;

    // Ensure we have a valid customer ID at this point
    if (!customerId) {
      throw new Error(
        'Failed to create or retrieve a valid Stripe customer ID',
      );
    }

    const session = await this.stripeService.createGoldCheckoutSession(
      priceId,
      customerId,
      metadata,
      successUrl || defaultSuccessUrl,
      cancelUrl || defaultCancelUrl,
    );

    const transaction = this.paymentTransactionRepository.create({
      amount: this.goldPackagePrices[goldPackage].price,
      currency: 'usd',
      status: PaymentStatus.PENDING,
      type: PaymentType.GOLD_PURCHASE,
      stripeSessionId: session.id,
      stripeCustomerId: customerId,
      user,
      userId: user.id,
      metadata,
    });

    await this.paymentTransactionRepository.save(transaction);

    return {
      sessionId: session.id,
      url: session.url || '',
    };
  }

  async cancelSubscription(userId: number): Promise<void> {
    this.logger.log(`Canceling subscription for user: ${userId}`);

    const user = await this.userService.findOne(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.stripeSubscriptionId) {
      throw new NotFoundException('No active subscription found');
    }

    try {
      // Get the current subscription details to get the current period end
      this.logger.log(
        `Retrieving subscription details for: ${user.stripeSubscriptionId}`,
      );
      const subscription = await this.stripeService.getSubscription(
        user.stripeSubscriptionId,
      );

      // Validate the subscription data
      if (!subscription || !subscription.current_period_end) {
        this.logger.error(
          `Invalid subscription data received: ${JSON.stringify(subscription)}`,
        );
        throw new Error('Invalid subscription data received from Stripe');
      }

      // Safely convert the current period end from Unix timestamp (seconds) to JavaScript Date
      let currentPeriodEnd: Date;
      try {
        if (
          typeof subscription.current_period_end === 'number' &&
          !isNaN(subscription.current_period_end) &&
          isFinite(subscription.current_period_end)
        ) {
          currentPeriodEnd = new Date(subscription.current_period_end * 1000);
          this.logger.log(
            `Current subscription period ends at: ${currentPeriodEnd.toISOString()}`,
          );
        } else {
          this.logger.warn(
            `Invalid current_period_end value: ${subscription.current_period_end}, using current date + 1 month`,
          );
          // Fallback to current date + 1 month if we can't get the actual end date
          currentPeriodEnd = new Date();
          currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 1);
        }
      } catch (error) {
        this.logger.error(
          `Error converting current_period_end: ${error.message}`,
        );
        // Fallback to current date + 1 month
        currentPeriodEnd = new Date();
        currentPeriodEnd.setMonth(currentPeriodEnd.getMonth() + 1);
      }

      // Cancel the subscription at period end
      this.logger.log(
        `Canceling subscription at Stripe: ${user.stripeSubscriptionId}`,
      );
      await this.stripeService.cancelSubscription(user.stripeSubscriptionId);

      // Update user's subscription status to indicate pending cancellation
      this.logger.log(
        `Updating user subscription status to cancel_at_period_end`,
      );
      await this.userService.updateSubscription(user.id, {
        isPremium: true, // User remains premium until the end of the billing period
        subscriptionStatus: 'cancel_at_period_end',
        premiumExpiresAt: currentPeriodEnd,
      });

      this.logger.log(
        `Subscription ${user.stripeSubscriptionId} canceled successfully for user ${userId}, will end on ${currentPeriodEnd.toISOString()}`,
      );

      // Format the expiration date safely for the email
      let expiresAtFormatted: string;
      try {
        expiresAtFormatted = currentPeriodEnd.toLocaleDateString();
      } catch (error) {
        this.logger.error(`Error formatting expiration date: ${error.message}`);
        expiresAtFormatted = 'at the end of your billing period';
      }

      // Send cancellation confirmation email
      try {
        await this.mailService.sendMail({
          to: user.email,
          subject: 'Premium Subscription Cancellation Confirmed',
          template: 'premium-subscription-canceled',
          context: {
            username: user.username,
            expiresAt: expiresAtFormatted,
            gameUrl:
              this.configService.get('CLIENT_URL') || 'http://localhost:3000',
            currentYear: new Date().getFullYear(),
          },
        });
        this.logger.log(
          `Cancellation confirmation email sent to ${user.email}`,
        );
      } catch (error) {
        this.logger.error(
          `Error sending cancellation email: ${error.message}`,
          error.stack,
        );
        // Don't throw error for email failure
      }
    } catch (error) {
      this.logger.error(
        `Error canceling subscription: ${error.message}`,
        error.stack,
      );
      throw new Error('Failed to cancel subscription');
    }
  }
}
