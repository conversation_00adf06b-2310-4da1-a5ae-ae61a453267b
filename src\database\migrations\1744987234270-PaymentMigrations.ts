import { MigrationInterface, QueryRunner } from 'typeorm';

export class PaymentMigrations1744987234270 implements MigrationInterface {
  name = 'PaymentMigrations1744987234270';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."payment_transaction_status_enum" AS ENUM('pending', 'succeeded', 'failed', 'refunded')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."payment_transaction_type_enum" AS ENUM('gold_purchase', 'premium_subscription')`,
    );
    await queryRunner.query(
      `CREATE TABLE "payment_transaction" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "amount" integer NOT NULL, "currency" character varying NOT NULL, "status" "public"."payment_transaction_status_enum" NOT NULL DEFAULT 'pending', "type" "public"."payment_transaction_type_enum" NOT NULL, "stripePaymentIntentId" character varying, "stripeCustomerId" character varying, "stripeSubscriptionId" character varying, "stripeSessionId" character varying, "metadata" json, "userId" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_82c3470854cf4642dfb0d7150cd" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "gold_purchase" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "goldAmount" integer NOT NULL, "realMoneyAmount" integer NOT NULL, "currency" character varying NOT NULL, "userId" integer NOT NULL, "paymentTransactionId" uuid NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_5bc28fd4bfccfccb616c26aadac" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD "stripeCustomerId" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD "stripeSubscriptionId" character varying`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."user_subscriptionstatus_enum" AS ENUM('active', 'inactive', 'past_due', 'canceled', 'unpaid', 'trialing')`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD "subscriptionStatus" "public"."user_subscriptionstatus_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "payment_transaction" ADD CONSTRAINT "FK_c30515be97af9ab6316b00ddeb1" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "gold_purchase" ADD CONSTRAINT "FK_6b4bf3382a039d5cc8a608ada4f" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "gold_purchase" ADD CONSTRAINT "FK_8605cf98d2a9e33d50b23a1427f" FOREIGN KEY ("paymentTransactionId") REFERENCES "payment_transaction"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "gold_purchase" DROP CONSTRAINT "FK_8605cf98d2a9e33d50b23a1427f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "gold_purchase" DROP CONSTRAINT "FK_6b4bf3382a039d5cc8a608ada4f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "payment_transaction" DROP CONSTRAINT "FK_c30515be97af9ab6316b00ddeb1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" DROP COLUMN "subscriptionStatus"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."user_subscriptionstatus_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" DROP COLUMN "stripeSubscriptionId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" DROP COLUMN "stripeCustomerId"`,
    );
    await queryRunner.query(`DROP TABLE "gold_purchase"`);
    await queryRunner.query(`DROP TABLE "payment_transaction"`);
    await queryRunner.query(
      `DROP TYPE "public"."payment_transaction_type_enum"`,
    );
    await queryRunner.query(
      `DROP TYPE "public"."payment_transaction_status_enum"`,
    );
  }
}
