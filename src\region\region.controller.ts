import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { RegionService } from './region.service';
import { CreateRegionDto } from './dto/create-region.dto';
import { UpdateRegionDto } from './dto/update-region.dto';
import { Public } from 'src/common/decorators/public.decorator';

@ApiTags('Regions')
@Controller('regions')
export class RegionController {
  constructor(private readonly regionService: RegionService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new region' })
  create(@Body() createRegionDto: CreateRegionDto) {
    return this.regionService.create(createRegionDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all regions' })
  @Public()
  findAll() {
    return this.regionService.findAll();
  }

  @Get('count')
  @ApiOperation({ summary: 'Get all regions count' })
  @Public()
  findAllCount() {
    return this.regionService.findAllCount();
  }

  @Get('search')
  @ApiOperation({ summary: 'Search regions by name' })
  @ApiQuery({
    name: 'name',
    required: true,
    description: 'Region name to search for',
  })
  async searchRegions(@Query('name') name: string) {
    return this.regionService.searchByName(name);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Find region by ID' })
  findById(@Param('id') id: string) {
    return this.regionService.findById(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a region' })
  update(@Param('id') id: string, @Body() updateRegionDto: UpdateRegionDto) {
    return this.regionService.update(id, updateRegionDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a region' })
  remove(@Param('id') id: string) {
    return this.regionService.remove(id);
  }

  // @Post('/seed')
  // @ApiOperation({ summary: 'Seed regions using data from restcountries.com' })
  // async seedRegions() {
  //   return this.regionService.seedRegions();
  // }
}
