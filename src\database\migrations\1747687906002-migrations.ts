import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1747687906002 implements MigrationInterface {
    name = 'Migrations1747687906002'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "region" ADD "latitude" double precision`);
        await queryRunner.query(`ALTER TABLE "region" ADD "longitude" double precision`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "region" DROP COLUMN "longitude"`);
        await queryRunner.query(`ALTER TABLE "region" DROP COLUMN "latitude"`);
    }

}
