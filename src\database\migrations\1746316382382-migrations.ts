import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1746316382382 implements MigrationInterface {
    name = 'Migrations1746316382382'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."user_activeautomode_enum" AS ENUM('war', 'work', 'none')`);
        await queryRunner.query(`ALTER TABLE "user" ADD "activeAutoMode" "public"."user_activeautomode_enum" NOT NULL DEFAULT 'none'`);
        await queryRunner.query(`ALTER TABLE "user" ADD "autoTargetId" character varying`);
        await queryRunner.query(`ALTER TABLE "user" ADD "autoModeExpiresAt" TIMESTAMP`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "autoModeExpiresAt"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "autoTargetId"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "activeAutoMode"`);
        await queryRunner.query(`DROP TYPE "public"."user_activeautomode_enum"`);
    }

}
