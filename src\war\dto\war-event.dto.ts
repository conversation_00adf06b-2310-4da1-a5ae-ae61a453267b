import { ApiProperty } from '@nestjs/swagger';

export class WarEventDto {
  @ApiProperty({
    description: 'Timestamp of the event',
    type: Date,
    example: '2023-04-15T14:30:00Z',
  })
  timestamp: Date;

  @ApiProperty({
    description: 'Description of the event',
    example: 'War declared by GeneralSupreme',
  })
  description: string;

  @ApiProperty({
    description: 'Type of event',
    example: 'declaration',
    enum: [
      'declaration',
      'start',
      'sea_phase_start',
      'sea_phase_end',
      'ground_phase_start',
      'significant_damage',
      'end',
    ],
  })
  eventType: string;
}
