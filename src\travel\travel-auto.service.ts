import { Injectable, Logger, OnModuleInit, Inject, forwardRef } from '@nestjs/common';
import { TravelAutoHandler } from './travel-auto.handler';
import { AutoActionService } from '../shared/auto-action.service';
import { AutoMode } from 'src/user/enums/auto-mode.enum';

@Injectable()
export class TravelAutoService implements OnModuleInit {
  private readonly logger = new Logger(TravelAutoService.name);

  constructor(
    private readonly travelAutoHandler: TravelAutoHandler,
    @Inject(forwardRef(() => AutoActionService))
    private readonly autoActionService: AutoActionService,
  ) {}

  /**
   * Register the travel auto handler with the auto action service on module init
   * Note: Travel uses a different completion mechanism (setTimeout) and should not use repeating auto actions
   */
  onModuleInit() {
    this.logger.log('Registering travel auto handler (for compatibility only - travel uses direct completion)');
    this.autoActionService.registerHandler(AutoMode.TRAVEL, this.travelAutoHandler);
  }

  /**
   * Schedule travel completion
   */
  async scheduleTravelCompletion(userId: number, travelId: string, endTime: Date): Promise<void> {
    this.logger.log(`Scheduling travel completion for user ${userId}, travel ${travelId}`);

    // Save auto settings
    await this.travelAutoHandler.saveAutoSettings(userId, travelId);

    // Calculate time until completion
    const now = new Date();
    const timeUntilCompletion = Math.max(0, endTime.getTime() - now.getTime());

    this.logger.log(`Travel will complete in ${timeUntilCompletion}ms at ${endTime}`);

    // Schedule execution at the exact time
    setTimeout(async () => {
      try {
        this.logger.log(`Executing scheduled travel completion for user ${userId}, travel ${travelId}`);
        // Directly execute the travel completion without creating a repeating auto action
        await this.travelAutoHandler.executeAction(userId, travelId);
      } catch (error) {
        this.logger.error(`Error in scheduled travel completion: ${error.message}`, error.stack);
      }
    }, timeUntilCompletion);

    this.logger.log(`Travel completion scheduled for ${endTime}`);
  }
}
