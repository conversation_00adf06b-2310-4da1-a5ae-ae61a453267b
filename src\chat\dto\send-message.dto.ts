import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsString,
  MaxLength,
  <PERSON><PERSON>ength,
} from 'class-validator';
import { MessageType } from '../entity/message.entity';

export class SendMessageDto {
  @ApiProperty({
    description: 'Message content',
    example: 'Hello, how are you?',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(2000) // Reasonable message length limit
  content: string;

  @ApiProperty({
    enum: MessageType,
    description: 'Type of message',
    example: MessageType.TEXT,
    default: MessageType.TEXT,
  })
  @IsEnum(MessageType)
  type: MessageType = MessageType.TEXT;
}
