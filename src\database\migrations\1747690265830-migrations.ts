import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1747690265830 implements MigrationInterface {
    name = 'Migrations1747690265830'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."travel_status_enum" AS ENUM('in_progress', 'completed', 'cancelled')`);
        await queryRunner.query(`CREATE TABLE "travel" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "startTime" TIMESTAMP NOT NULL, "endTime" TIMESTAMP NOT NULL, "status" "public"."travel_status_enum" NOT NULL DEFAULT 'in_progress', "distance" double precision NOT NULL, "travelTime" double precision NOT NULL, "seaCrossing" boolean NOT NULL DEFAULT false, "sameState" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "userId" integer, "sourceRegionId" uuid, "destinationRegionId" uuid, CONSTRAINT "PK_657b63ec7adcf2ecf757a490a67" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."travel_permission_status_enum" AS ENUM('pending', 'approved', 'rejected')`);
        await queryRunner.query(`CREATE TABLE "travel_permission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" "public"."travel_permission_status_enum" NOT NULL DEFAULT 'pending', "reason" text, "responseMessage" text, "respondedAt" TIMESTAMP, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "userId" integer, "sourceRegionId" uuid, "destinationRegionId" uuid, "destinationStateId" uuid, "respondedById" integer, CONSTRAINT "PK_e6f3b533cf6a7625daa4ba97ed5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "user" ADD "isTraveling" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "travel" ADD CONSTRAINT "FK_0f1f939cfb73a792c27ed58a462" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "travel" ADD CONSTRAINT "FK_b34a01599dc5aed5ecf9e085d79" FOREIGN KEY ("sourceRegionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "travel" ADD CONSTRAINT "FK_8190440d3d2b9668ae6ef3250cf" FOREIGN KEY ("destinationRegionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "travel_permission" ADD CONSTRAINT "FK_594c1b3d00585d18929109e3a8b" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "travel_permission" ADD CONSTRAINT "FK_ca0a268851f34e2b0a6cd765e78" FOREIGN KEY ("sourceRegionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "travel_permission" ADD CONSTRAINT "FK_1cf2c60f9b3de174825ad8cdf13" FOREIGN KEY ("destinationRegionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "travel_permission" ADD CONSTRAINT "FK_410d65b43f183341fcef1959810" FOREIGN KEY ("destinationStateId") REFERENCES "state"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "travel_permission" ADD CONSTRAINT "FK_74df0a13c8839205146c1228639" FOREIGN KEY ("respondedById") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "travel_permission" DROP CONSTRAINT "FK_74df0a13c8839205146c1228639"`);
        await queryRunner.query(`ALTER TABLE "travel_permission" DROP CONSTRAINT "FK_410d65b43f183341fcef1959810"`);
        await queryRunner.query(`ALTER TABLE "travel_permission" DROP CONSTRAINT "FK_1cf2c60f9b3de174825ad8cdf13"`);
        await queryRunner.query(`ALTER TABLE "travel_permission" DROP CONSTRAINT "FK_ca0a268851f34e2b0a6cd765e78"`);
        await queryRunner.query(`ALTER TABLE "travel_permission" DROP CONSTRAINT "FK_594c1b3d00585d18929109e3a8b"`);
        await queryRunner.query(`ALTER TABLE "travel" DROP CONSTRAINT "FK_8190440d3d2b9668ae6ef3250cf"`);
        await queryRunner.query(`ALTER TABLE "travel" DROP CONSTRAINT "FK_b34a01599dc5aed5ecf9e085d79"`);
        await queryRunner.query(`ALTER TABLE "travel" DROP CONSTRAINT "FK_0f1f939cfb73a792c27ed58a462"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "isTraveling"`);
        await queryRunner.query(`DROP TABLE "travel_permission"`);
        await queryRunner.query(`DROP TYPE "public"."travel_permission_status_enum"`);
        await queryRunner.query(`DROP TABLE "travel"`);
        await queryRunner.query(`DROP TYPE "public"."travel_status_enum"`);
    }

}
