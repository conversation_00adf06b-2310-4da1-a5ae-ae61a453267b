import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1749924499741 implements MigrationInterface {
    name = 'Migrations1749924499741'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."state_governmenttype_enum" AS ENUM('dictatorship', 'republic')`);
        await queryRunner.query(`ALTER TABLE "state" ADD "governmentType" "public"."state_governmenttype_enum" NOT NULL DEFAULT 'republic'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "state" DROP COLUMN "governmentType"`);
        await queryRunner.query(`DROP TYPE "public"."state_governmenttype_enum"`);
    }

}
