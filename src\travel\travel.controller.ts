import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  Req,
  BadRequestException,
  Patch,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { TravelService } from './travel.service';
import {
  InitiateTravelDto,
  RequestTravelPermissionDto,
  RespondToPermissionRequestDto,
  TravelTimeEstimateDto,
} from './dto/initiate-travel.dto';
import { AuthGuard } from '../common/guards/auth.guard';
import { Request } from 'express';
import { Travel } from './entity/travel.entity';
import { TravelPermission } from './entity/travel-permission.entity';
import { BullMQAutoActionService } from '../shared/bullmq-auto-action.service';

@ApiTags('Travel')
@Controller('travel')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class TravelController {
  constructor(
    private readonly travelService: TravelService,
    private readonly bullMQAutoActionService: BullMQAutoActionService,
  ) {}

  @Post('initiate')
  @ApiOperation({ summary: 'Initiate travel to another region' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Travel initiated successfully',
    type: Travel,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input or user is already traveling',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'User does not have permission to travel to the region',
  })
  async initiateTravel(
    @Body() initiateTravelDto: InitiateTravelDto,
    @Req() req: Request & { user: { userId: number } },
  ): Promise<Travel> {
    const userId = req.user.userId;
    if (!userId || isNaN(userId)) {
      throw new BadRequestException('Invalid user ID');
    }
    return this.travelService.initiateTravel(userId, initiateTravelDto);
  }

  @Get('current')
  @ApiOperation({ summary: "Get the user's current travel status" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Returns the user's current travel or null if not traveling",
    type: Travel,
  })
  async getCurrentTravel(
    @Req() req: Request & { user: { userId: number } },
  ): Promise<Travel | null> {
    const userId = req.user.userId;
    if (!userId || isNaN(userId)) {
      throw new BadRequestException('Invalid user ID');
    }
    return this.travelService.getCurrentTravel(userId);
  }

  @Post('cancel/:id')
  @ApiOperation({ summary: 'Cancel an in-progress travel' })
  @ApiParam({ name: 'id', description: 'Travel ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Travel cancelled successfully',
    type: Travel,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Travel not found or already completed',
  })
  async cancelTravel(
    @Param('id') id: string,
    @Req() req: Request & { user: { userId: number } },
  ): Promise<Travel> {
    const userId = req.user.userId;
    if (!userId || isNaN(userId)) {
      throw new BadRequestException('Invalid user ID');
    }
    return this.travelService.cancelTravel(userId, id);
  }

  @Post('time-estimate')
  @ApiOperation({ summary: 'Get estimated travel time between regions' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Returns travel time estimate',
  })
  async getTravelTimeEstimate(
    @Body() travelTimeEstimateDto: TravelTimeEstimateDto,
  ): Promise<{
    distance: number;
    travelTime: number;
    seaCrossing: boolean;
    sameState: boolean;
    cost: number;
    currency: string;
  }> {
    return this.travelService.getTravelTimeEstimate(travelTimeEstimateDto);
  }

  @Post('request-permission')
  @ApiOperation({ summary: 'Request permission to travel to a region' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Permission request created successfully',
    type: TravelPermission,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description:
      'Invalid input, user is already traveling, or already has a pending request',
  })
  async requestTravelPermission(
    @Body() requestTravelPermissionDto: RequestTravelPermissionDto,
    @Req() req: Request & { user: { userId: number } },
  ): Promise<TravelPermission> {
    const userId = req.user.userId;
    if (!userId || isNaN(userId)) {
      throw new BadRequestException('Invalid user ID');
    }
    return this.travelService.requestTravelPermission(
      userId,
      requestTravelPermissionDto,
    );
  }

  @Patch('permission-requests/:id')
  @ApiOperation({ summary: 'Respond to a travel permission request' })
  @ApiParam({ name: 'id', description: 'Permission request ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Permission request updated successfully',
    type: TravelPermission,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Permission request not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description:
      'Only the leader of the destination state can respond to this request',
  })
  async respondToPermissionRequest(
    @Param('id') id: string,
    @Body() respondToPermissionRequestDto: RespondToPermissionRequestDto,
    @Req() req: Request & { user: { userId: number } },
  ): Promise<TravelPermission> {
    const userId = req.user.userId;
    if (!userId || isNaN(userId)) {
      throw new BadRequestException('Invalid user ID');
    }
    return this.travelService.respondToPermissionRequest(
      userId,
      id,
      respondToPermissionRequestDto,
    );
  }

  @Get('permission-requests/state')
  @ApiOperation({
    summary: 'Get all permission requests for states led by the user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Returns all permission requests for the user\'s states',
    type: [TravelPermission],
  })
  async getStatePermissionRequests(
    @Req() req: Request & { user: { userId: number } },
  ): Promise<TravelPermission[]> {
    const userId = req.user.userId;
    if (!userId || isNaN(userId)) {
      throw new BadRequestException('Invalid user ID');
    }
    return this.travelService.getStatePermissionRequests(userId);
  }

  @Get('permission-requests/user')
  @ApiOperation({ summary: "Get all of the user's permission requests" })
  @ApiResponse({
    status: HttpStatus.OK,
    description: "Returns all of the user's permission requests",
    type: [TravelPermission],
  })
  async getUserPermissionRequests(
    @Req() req: Request & { user: { userId: number } },
  ): Promise<TravelPermission[]> {
    const userId = req.user.userId;
    if (!userId || isNaN(userId)) {
      throw new BadRequestException('Invalid user ID');
    }
    return this.travelService.getUserPermissionRequests(userId);
  }

  @Post('cleanup-travel-jobs')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Clean up stuck travel auto jobs (debug endpoint)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Travel auto jobs cleanup completed',
    schema: {
      type: 'object',
      properties: {
        totalTravelJobs: { type: 'number', description: 'Total travel jobs found' },
        removedJobs: { type: 'number', description: 'Number of jobs removed' },
        errors: { type: 'array', items: { type: 'string' }, description: 'Any errors encountered' }
      }
    }
  })
  async cleanupTravelAutoJobs(): Promise<{
    totalTravelJobs: number;
    removedJobs: number;
    errors: string[];
  }> {
    return this.bullMQAutoActionService.cleanupTravelAutoJobs();
  }
}
