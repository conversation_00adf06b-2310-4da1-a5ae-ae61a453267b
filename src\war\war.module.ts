import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { War } from './entity/war.entity';
import { WarHistory } from './entity/war-history.entity';
import { WarReport } from './entity/war-report.entity';
import { WarService } from './war.service';
import { WarController } from './war.controller';
import { WarAnalyticsService } from './war-analytics.service';
import { WarAnalyticsAdvancedService } from './war-analytics-advanced.service';
import { WarHistoryService } from './war-history.service';
import { WarReportService } from './war-report.service';
import { Region } from '../region/entity/region.entity';
import { State } from '../state/entity/state.entity';
import { User } from '../user/entity/user.entity';
import { NotificationModule } from '../notification/notification.module';
import { UserModule } from '../user/user.module';
import { WarAutoService } from './war-auto.service';
import { WarAutoHandler } from './war-auto.handler';
import { StateModule } from 'src/state/state.module';
import { StateElectionModule } from '../state-election/state-election.module';

import { SharedModule } from 'src/shared/shared.module';
import { QueueModule } from '../queue/queue.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([War, WarHistory, WarReport, Region, State, User]),
    NotificationModule,
    StateModule,
    forwardRef(() => StateElectionModule),

    forwardRef(() => UserModule),
    forwardRef(() => SharedModule),
    forwardRef(() => QueueModule),
  ],
  providers: [
    WarService,
    WarAnalyticsService,
    WarAnalyticsAdvancedService,
    WarHistoryService,
    WarReportService,
    WarAutoService,
    WarAutoHandler,
  ],
  controllers: [WarController],
  exports: [
    WarService,
    WarAnalyticsService,
    WarAnalyticsAdvancedService,
    WarHistoryService,
    WarReportService,
    WarAutoService,
    WarAutoHandler,
  ],
})
export class WarModule {}
