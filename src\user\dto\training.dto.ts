// src/training/dto/train.dto.ts
import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsEnum, Min } from 'class-validator';

export enum TrainingType {
  STRENGTH = 'strength',
  ENDURANCE = 'endurance',
  INTELLIGENCE = 'intelligence',
}

export enum TrainingCurrency {
  GOLD = 'gold',
  MONEY = 'money',
}

export class TrainDto {
  @ApiProperty({ description: 'Type of training', enum: TrainingType })
  @IsEnum(TrainingType)
  trainingType: TrainingType;

  @ApiProperty({ description: 'Training duration in minutes', example: 30 })
  @IsNumber()
  @Min(1)
  duration: number;

  @ApiProperty({
    description: 'Currency type used for training',
    enum: TrainingCurrency,
  })
  @IsEnum(TrainingCurrency)
  currency: TrainingCurrency;

  @ApiProperty({
    description: 'Calculated cost amount for the training',
    example: 190,
  })
  @IsNumber()
  @Min(0)
  amount: number;
}
