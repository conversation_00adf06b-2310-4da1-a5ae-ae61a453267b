import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1746308350989 implements MigrationInterface {
    name = 'Migrations1746308350989'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "factory" DROP CONSTRAINT "FK_7cd684a105e39670f86331d2846"`);
        await queryRunner.query(`ALTER TABLE "factory" ALTER COLUMN "ownerId" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "factory" ADD CONSTRAINT "FK_7cd684a105e39670f86331d2846" FOREIGN KEY ("ownerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "factory" DROP CONSTRAINT "FK_7cd684a105e39670f86331d2846"`);
        await queryRunner.query(`ALTER TABLE "factory" ALTER COLUMN "ownerId" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "factory" ADD CONSTRAINT "FK_7cd684a105e39670f86331d2846" FOREIGN KEY ("ownerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
