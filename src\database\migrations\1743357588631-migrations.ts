import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1743357588631 implements MigrationInterface {
  name = 'Migrations1743357588631';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "strength" SET DEFAULT '10'`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "endurance" SET DEFAULT '10'`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "intelligence" SET DEFAULT '10'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "intelligence" SET DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "endurance" SET DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "strength" SET DEFAULT '0'`,
    );
  }
}
