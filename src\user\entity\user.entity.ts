import {
  <PERSON>tity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  BeforeInsert,
  OneToOne,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import * as bcrypt from 'bcrypt';
import { Party } from '../../party/entity/party.entity';
import { Exclude, Type } from 'class-transformer';
import { Region } from '../../region/entity/region.entity';
import { Factory } from '../../factory/entity/factory.entity';
import { WorkSession } from '../../work-session/entity/work-session.entity';
import { AutoMode } from '../enums/auto-mode.enum';
import { PartyJoinRequest } from 'src/party/entity/party-join-request.entity';
import { Travel } from '../../travel/entity/travel.entity';
// import { Nation } from './nation.entity';
// import { Residency } from './residency.entity';

@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  username: string;

  @Column({ unique: true })
  email: string;

  @Column()
  @Exclude({ toPlainOnly: true })
  password: string;

  @Column({ nullable: true })
  resetToken?: string;

  @Column({ type: 'timestamp', nullable: true })
  resetTokenExpires?: Date | null;

  @Column({ type: 'varchar', nullable: true })
  avatarUrl?: string | null;

  @Column({ default: 1 })
  level: number;

  @Column({ default: 0 })
  experience: number;

  @Column({ default: 100 })
  energy: number;

  @Column({ default: 10 })
  strength: number;

  @Column({ default: 10 })
  endurance: number;

  @Column({ default: 10 })
  intelligence: number;

  @Column({ default: 1000 })
  gold: number;

  @Column({ default: 10000 })
  money: number;

  @Column({ type: 'timestamp', nullable: true })
  trainingExpiresAt?: Date;

  @Column({ type: 'text', nullable: true })
  aboutMe: string;

  @Column({ default: false })
  isPremium: boolean;

  @Column({ type: 'timestamp', nullable: true })
  premiumExpiresAt?: Date;

  @Column({ nullable: true })
  stripeCustomerId?: string;

  @Column({ nullable: true })
  stripeSubscriptionId?: string;

  @Column({
    type: 'enum',
    enum: [
      'active',
      'inactive',
      'past_due',
      'canceled',
      'unpaid',
      'trialing',
      'cancel_at_period_end',
    ],
    nullable: true,
  })
  subscriptionStatus?: string;

  //   @ManyToOne(() => Nation, (nation) => nation.citizens)
  //   nation: Nation;

  //   @ManyToOne(() => Residency, (residency) => residency.residents)
  //   residency: Residency;

  @Column({ nullable: true })
  verificationToken?: string;

  @Column({ default: false })
  isActive: boolean;

  @OneToOne(() => Party, (party) => party.leader, { nullable: true })
  @Type(() => Party)
  @Exclude({ toPlainOnly: true })
  leadingParty: Party | null;

  @ManyToOne(() => Party, (party) => party.members, { nullable: true })
  @Type(() => Party)
  @Exclude({ toPlainOnly: true })
  memberOfParty: Party | null;

  @ManyToOne(() => Region, (region) => region.users, { nullable: false })
  region: Region;

  @OneToMany(() => Factory, (factory) => factory.owner)
  ownedFactories: Factory[];

  @OneToMany(() => WorkSession, (workSession) => workSession.worker)
  workSessions: WorkSession[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'enum', enum: AutoMode, default: AutoMode.NONE })
  activeAutoMode: AutoMode;

  @Column({ type: 'varchar', nullable: true })
  autoTargetId: string;

  @Column({ type: 'timestamp', nullable: true })
  autoModeExpiresAt: Date;

  @ManyToOne(() => Factory, (factory) => factory.workers, { nullable: true })
  workingAt: Factory | null;

  @OneToMany(() => PartyJoinRequest, (request) => request.user)
  partyJoinRequests: PartyJoinRequest[];

  @OneToMany(() => Travel, (travel) => travel.user)
  travels: Travel[];

  @Column({ default: false })
  isTraveling: boolean;

  @BeforeInsert()
  async hashPassword() {
    this.password = await bcrypt.hash(this.password, 10);
  }

  updateEnergy(): void {
    const now = new Date();
    const lastUpdate = this.updatedAt || this.createdAt;

    // Calculate time passed in 30-minute intervals
    const halfHoursPassed =
      (now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 30);

    // Energy regeneration rate: 10 per 30 minutes for non-premium, 20 per 30 minutes for premium
    const energyRegen = Math.floor(
      halfHoursPassed * (this.isPremium ? 20 : 10),
    );
    const maxEnergy = this.isPremium ? 200 : 100;

    this.energy = Math.min(maxEnergy, this.energy + energyRegen);
  }

  private getRequiredExperienceForLevel(level: number): number {
    // Formula: 100 * (level ^ 2)
    return 100 * Math.pow(level, 2);
  }

  checkAndUpdateLevel(): boolean {
    const hasLeveledUp =
      this.experience >= this.getRequiredExperienceForLevel(this.level + 1);
    if (hasLeveledUp) {
      this.level += 1;
    }
    return hasLeveledUp;
  }
}
