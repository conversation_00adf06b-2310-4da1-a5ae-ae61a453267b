import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
} from 'class-validator';

export enum PremiumPlan {
  MONTHLY = 'monthly',
  SEMIANNUAL = 'semiannual',
  YEARLY = 'yearly',
}

export class CreateCheckoutSessionDto {
  @ApiProperty({
    description: 'Premium subscription plan',
    enum: PremiumPlan,
    example: PremiumPlan.MONTHLY,
  })
  @IsEnum(PremiumPlan)
  @IsNotEmpty()
  plan: PremiumPlan;

  @ApiProperty({
    description: 'Success URL to redirect after successful payment',
    example: 'https://example.com/success',
    required: false,
  })
  @IsOptional()
  successUrl?: string;

  @ApiProperty({
    description: 'Cancel URL to redirect after canceled payment',
    example: 'https://example.com/cancel',
    required: false,
  })
  @IsOptional()
  cancelUrl?: string;
}
