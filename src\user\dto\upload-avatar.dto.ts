import { ApiProperty } from '@nestjs/swagger';

export class UploadAvatarResponseDto {
  @ApiProperty({ 
    example: 'https://storage.example.com/avatars/123/1234567890-abc123.jpg',
    description: 'URL of the uploaded avatar image'
  })
  avatarUrl: string;

  @ApiProperty({ 
    example: 'avatars/123/1234567890-abc123.jpg',
    description: 'Storage key of the uploaded file'
  })
  key: string;

  @ApiProperty({ 
    example: 245760,
    description: 'Size of the processed image in bytes'
  })
  size: number;

  @ApiProperty({ 
    example: 'image/jpeg',
    description: 'MIME type of the processed image'
  })
  mimeType: string;
}
