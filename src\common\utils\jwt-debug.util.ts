import { Logger } from '@nestjs/common';

export class JwtDebugUtil {
  private static readonly logger = new Logger(JwtDebugUtil.name);

  /**
   * Debug JWT token payload, especially for non-ASCII characters
   */
  static debugJwtPayload(payload: any, context: string = 'JWT'): void {
    this.logger.debug(`=== ${context} Payload Debug ===`);
    
    if (!payload) {
      this.logger.debug('Payload is null or undefined');
      return;
    }

    this.logger.debug(`Payload type: ${typeof payload}`);
    this.logger.debug(`Payload keys: ${Object.keys(payload).join(', ')}`);

    if (payload.username) {
      this.debugUsername(payload.username);
    }

    if (payload.userId) {
      this.logger.debug(`User ID: ${payload.userId} (type: ${typeof payload.userId})`);
    }

    this.logger.debug(`Full payload: ${JSON.stringify(payload, null, 2)}`);
    this.logger.debug(`=== End ${context} Debug ===`);
  }

  /**
   * Debug username encoding issues
   */
  static debugUsername(username: string): void {
    if (!username || typeof username !== 'string') {
      this.logger.debug(`Username is not a valid string: ${username} (type: ${typeof username})`);
      return;
    }

    this.logger.debug(`Username: "${username}"`);
    this.logger.debug(`Username length: ${username.length}`);
    this.logger.debug(`Username byte length: ${Buffer.from(username, 'utf8').length}`);
    
    // Check if username contains non-ASCII characters
    const hasNonAscii = /[^\x00-\x7F]/.test(username);
    this.logger.debug(`Contains non-ASCII characters: ${hasNonAscii}`);

    if (hasNonAscii) {
      // Show character codes for debugging
      const charCodes = Array.from(username).map(char => ({
        char,
        code: char.charCodeAt(0),
        hex: char.charCodeAt(0).toString(16)
      }));
      this.logger.debug(`Character breakdown: ${JSON.stringify(charCodes, null, 2)}`);

      // Test UTF-8 encoding/decoding
      try {
        const encoded = Buffer.from(username, 'utf8').toString('utf8');
        const matches = encoded === username;
        this.logger.debug(`UTF-8 round-trip test: ${matches ? 'PASS' : 'FAIL'}`);
        if (!matches) {
          this.logger.debug(`Original: "${username}"`);
          this.logger.debug(`After UTF-8 round-trip: "${encoded}"`);
        }
      } catch (error) {
        this.logger.error(`UTF-8 encoding test failed: ${error.message}`);
      }
    }
  }

  /**
   * Debug JWT token string
   */
  static debugJwtToken(token: string, context: string = 'JWT'): void {
    this.logger.debug(`=== ${context} Token Debug ===`);
    
    if (!token || typeof token !== 'string') {
      this.logger.debug(`Token is not a valid string: ${token} (type: ${typeof token})`);
      return;
    }

    this.logger.debug(`Token length: ${token.length}`);
    this.logger.debug(`Token starts with: ${token.substring(0, 50)}...`);
    
    // Check JWT structure
    const parts = token.split('.');
    this.logger.debug(`JWT parts count: ${parts.length} (should be 3)`);
    
    if (parts.length === 3) {
      try {
        // Decode header
        const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
        this.logger.debug(`Header: ${JSON.stringify(header)}`);
        
        // Decode payload (without verification)
        const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
        this.logger.debug(`Payload preview: ${JSON.stringify(payload, null, 2)}`);
        
        if (payload.username) {
          this.debugUsername(payload.username);
        }
      } catch (error) {
        this.logger.error(`Failed to decode JWT parts: ${error.message}`);
      }
    }
    
    this.logger.debug(`=== End ${context} Token Debug ===`);
  }

  /**
   * Compare two usernames for encoding differences
   */
  static compareUsernames(username1: string, username2: string, label1: string = 'Username 1', label2: string = 'Username 2'): void {
    this.logger.debug(`=== Username Comparison: ${label1} vs ${label2} ===`);
    
    this.logger.debug(`${label1}: "${username1}" (length: ${username1?.length || 0})`);
    this.logger.debug(`${label2}: "${username2}" (length: ${username2?.length || 0})`);
    
    const areEqual = username1 === username2;
    this.logger.debug(`Usernames equal: ${areEqual}`);
    
    if (!areEqual && username1 && username2) {
      // Show byte-level comparison
      const bytes1 = Buffer.from(username1, 'utf8');
      const bytes2 = Buffer.from(username2, 'utf8');
      
      this.logger.debug(`${label1} bytes: [${Array.from(bytes1).join(', ')}]`);
      this.logger.debug(`${label2} bytes: [${Array.from(bytes2).join(', ')}]`);
      
      // Try to identify the encoding issue
      if (bytes1.length !== bytes2.length) {
        this.logger.debug('Different byte lengths - likely encoding issue');
      }
    }
    
    this.logger.debug(`=== End Username Comparison ===`);
  }
}
