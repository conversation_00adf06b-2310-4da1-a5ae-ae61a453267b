services:
  backend:
    build: .
    container_name: nest-app
    ports:
      - '3000:3000'
    env_file:
      - .env
    restart: always
    depends_on:
      - redis
      - postgres

  redis:
    image: redis:7-alpine
    container_name: redis
    ports:
      - '6379:6379'
    restart: always
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  postgres:
    image: postgres:16-alpine
    container_name: postgres
    ports:
      - '5432:5432'
    restart: always
    env_file:
      - .env
    volumes:
      - pg_data:/var/lib/postgresql/data

volumes:
  redis_data:
  pg_data:
