import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1744316176838 implements MigrationInterface {
  name = 'Migrations1744316176838';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "user" ADD "memberOfPartyId" uuid`);
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "FK_d30db2cc45adf6bfea070ef63dd" FOREIGN KEY ("memberOfPartyId") REFERENCES "party"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "FK_d30db2cc45adf6bfea070ef63dd"`,
    );
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "memberOfPartyId"`);
  }
}
