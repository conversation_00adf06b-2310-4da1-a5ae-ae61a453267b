import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1744670066333 implements MigrationInterface {
  name = 'Migrations1744670066333';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."war_report_wartype_enum" AS ENUM('ground', 'sea', 'air', 'revolution')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."war_report_wartarget_enum" AS ENUM('conquest', 'resistance', 'resources', 'revolution')`,
    );
    await queryRunner.query(
      `CREATE TABLE "war_report" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "warId" character varying NOT NULL, "title" character varying NOT NULL, "summary" text NOT NULL, "warType" "public"."war_report_wartype_enum" NOT NULL, "warTarget" "public"."war_report_wartarget_enum" NOT NULL, "startDate" TIMESTAMP NOT NULL, "endDate" TIMESTAMP NOT NULL, "durationHours" double precision NOT NULL, "attackerName" character varying NOT NULL, "defenderName" character varying NOT NULL, "winner" character varying NOT NULL, "battleStatistics" json NOT NULL, "timeline" json NOT NULL, "topDamageDealers" json NOT NULL, "outcome" json NOT NULL, "visualData" json, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_f430e23330323dc150d45da6705" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."war_history_wartype_enum" AS ENUM('ground', 'sea', 'air', 'revolution')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."war_history_wartarget_enum" AS ENUM('conquest', 'resistance', 'resources', 'revolution')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."war_history_finalstatus_enum" AS ENUM('pending', 'ground_phase', 'ended')`,
    );
    await queryRunner.query(
      `CREATE TABLE "war_history" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "warId" character varying NOT NULL, "warType" "public"."war_history_wartype_enum" NOT NULL, "warTarget" "public"."war_history_wartarget_enum" NOT NULL, "declaration" text, "attackerStateName" character varying, "attackerRegionName" character varying, "defenderStateName" character varying, "defenderRegionName" character varying, "targetRegionName" character varying, "declaredByUsername" character varying NOT NULL, "declaredAt" TIMESTAMP NOT NULL, "startedAt" TIMESTAMP, "endedAt" TIMESTAMP, "durationHours" double precision, "finalStatus" "public"."war_history_finalstatus_enum" NOT NULL, "winner" character varying, "totalAttackerDamage" double precision NOT NULL DEFAULT '0', "totalDefenderDamage" double precision NOT NULL DEFAULT '0', "totalParticipants" integer NOT NULL DEFAULT '0', "attackerParticipants" integer NOT NULL DEFAULT '0', "defenderParticipants" integer NOT NULL DEFAULT '0', "totalEnergySpent" integer NOT NULL DEFAULT '0', "resourcesCaptured" json, "topDamageDealers" json NOT NULL, "keyEvents" json NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_d0f04a38540fd01f26ea7f32af6" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."notification_type_enum" AS ENUM('war_declared', 'war_joined', 'war_phase_changed', 'war_ended', 'war_damage_milestone', 'region_conquered', 'resources_captured', 'revolution_succeeded', 'system')`,
    );
    await queryRunner.query(
      `CREATE TABLE "notification" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "type" "public"."notification_type_enum" NOT NULL, "title" character varying NOT NULL, "content" text NOT NULL, "entityId" character varying, "entityType" character varying, "isRead" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "userId" integer, CONSTRAINT "PK_705b6c7cdf9b2c2ff7ac7872cb7" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "war" DROP COLUMN "seaPhaseWon"`);
    await queryRunner.query(
      `ALTER TABLE "war" DROP COLUMN "revolutionSupportPercentage"`,
    );
    await queryRunner.query(`ALTER TABLE "war" DROP COLUMN "attackerBonus"`);
    await queryRunner.query(`ALTER TABLE "war" DROP COLUMN "defenderBonus"`);
    await queryRunner.query(`ALTER TABLE "war" DROP COLUMN "cooldownUntil"`);
    await queryRunner.query(`ALTER TABLE "war" DROP COLUMN "createdAt"`);
    await queryRunner.query(`ALTER TABLE "war" DROP COLUMN "updatedAt"`);
    await queryRunner.query(
      `ALTER TYPE "public"."war_status_enum" RENAME TO "war_status_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."war_status_enum" AS ENUM('pending', 'ground_phase', 'ended')`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ALTER COLUMN "status" TYPE "public"."war_status_enum" USING "status"::"text"::"public"."war_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ALTER COLUMN "status" SET DEFAULT 'pending'`,
    );
    await queryRunner.query(`DROP TYPE "public"."war_status_enum_old"`);
    await queryRunner.query(
      `ALTER TABLE "war" ALTER COLUMN "warTarget" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ALTER COLUMN "declaration" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ALTER COLUMN "declaredAt" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "notification" ADD CONSTRAINT "FK_1ced25315eb974b73391fb1c81b" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "notification" DROP CONSTRAINT "FK_1ced25315eb974b73391fb1c81b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ALTER COLUMN "declaredAt" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ALTER COLUMN "declaration" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ALTER COLUMN "warTarget" SET DEFAULT 'conquest'`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."war_status_enum_old" AS ENUM('pending', 'sea_phase', 'ground_phase', 'ended')`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ALTER COLUMN "status" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ALTER COLUMN "status" TYPE "public"."war_status_enum_old" USING "status"::"text"::"public"."war_status_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ALTER COLUMN "status" SET DEFAULT 'pending'`,
    );
    await queryRunner.query(`DROP TYPE "public"."war_status_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."war_status_enum_old" RENAME TO "war_status_enum"`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ADD "updatedAt" TIMESTAMP NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(`ALTER TABLE "war" ADD "cooldownUntil" TIMESTAMP`);
    await queryRunner.query(
      `ALTER TABLE "war" ADD "defenderBonus" double precision NOT NULL DEFAULT '1'`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ADD "attackerBonus" double precision NOT NULL DEFAULT '1'`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ADD "revolutionSupportPercentage" double precision`,
    );
    await queryRunner.query(
      `ALTER TABLE "war" ADD "seaPhaseWon" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(`DROP TABLE "notification"`);
    await queryRunner.query(`DROP TYPE "public"."notification_type_enum"`);
    await queryRunner.query(`DROP TABLE "war_history"`);
    await queryRunner.query(
      `DROP TYPE "public"."war_history_finalstatus_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."war_history_wartarget_enum"`);
    await queryRunner.query(`DROP TYPE "public"."war_history_wartype_enum"`);
    await queryRunner.query(`DROP TABLE "war_report"`);
    await queryRunner.query(`DROP TYPE "public"."war_report_wartarget_enum"`);
    await queryRunner.query(`DROP TYPE "public"."war_report_wartype_enum"`);
  }
}
