import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1743355955634 implements MigrationInterface {
  name = 'Migrations1743355955634';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."factory_type_enum" AS ENUM('GOLD', 'MONEY')`,
    );
    await queryRunner.query(
      `CREATE TABLE "factory" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "type" "public"."factory_type_enum" NOT NULL DEFAULT 'MONEY', "regionId" uuid NOT NULL, "ownerId" integer NOT NULL, "wage" numeric(10,2) NOT NULL, "maxWorkers" integer NOT NULL, "currentWorkers" integer NOT NULL DEFAULT '0', "energyCost" integer NOT NULL, "resourcePerWork" numeric(10,2) NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_1372e5a7d114a3fa80736ba66bb" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "work_session" ("id" SERIAL NOT NULL, "factoryId" integer NOT NULL, "workerId" integer NOT NULL, "energySpent" double precision NOT NULL, "wageEarned" double precision NOT NULL, "resourceEarned" double precision NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_77af700aa2da211636470a6352b" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `ALTER TABLE "region" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "region" ADD "updatedAt" TIMESTAMP NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "party" ADD "isActive" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "party" ADD "updatedAt" TIMESTAMP NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(`ALTER TABLE "party" ADD "leaderId" integer`);
    await queryRunner.query(
      `ALTER TABLE "user" ADD "isPremium" boolean NOT NULL DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD "premiumExpiresAt" TIMESTAMP`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD "createdAt" TIMESTAMP NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD "updatedAt" TIMESTAMP NOT NULL DEFAULT now()`,
    );
    await queryRunner.query(`ALTER TABLE "party" DROP COLUMN "description"`);
    await queryRunner.query(`ALTER TABLE "party" ADD "description" text`);
    await queryRunner.query(
      `ALTER TABLE "factory" ADD CONSTRAINT "FK_7cd684a105e39670f86331d2846" FOREIGN KEY ("ownerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "factory" ADD CONSTRAINT "FK_1fef1ea60770912790b96e84569" FOREIGN KEY ("regionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "party" ADD CONSTRAINT "FK_810e109127f899b1701320b4a37" FOREIGN KEY ("leaderId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "work_session" ADD CONSTRAINT "FK_4f52dfffd3a99eed8d2380b7992" FOREIGN KEY ("factoryId") REFERENCES "factory"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "work_session" ADD CONSTRAINT "FK_9eed69d055c3234bc27722c8aed" FOREIGN KEY ("workerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "work_session" DROP CONSTRAINT "FK_9eed69d055c3234bc27722c8aed"`,
    );
    await queryRunner.query(
      `ALTER TABLE "work_session" DROP CONSTRAINT "FK_4f52dfffd3a99eed8d2380b7992"`,
    );
    await queryRunner.query(
      `ALTER TABLE "party" DROP CONSTRAINT "FK_810e109127f899b1701320b4a37"`,
    );
    await queryRunner.query(
      `ALTER TABLE "factory" DROP CONSTRAINT "FK_1fef1ea60770912790b96e84569"`,
    );
    await queryRunner.query(
      `ALTER TABLE "factory" DROP CONSTRAINT "FK_7cd684a105e39670f86331d2846"`,
    );
    await queryRunner.query(`ALTER TABLE "party" DROP COLUMN "description"`);
    await queryRunner.query(
      `ALTER TABLE "party" ADD "description" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "updatedAt"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "createdAt"`);
    await queryRunner.query(
      `ALTER TABLE "user" DROP COLUMN "premiumExpiresAt"`,
    );
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "isPremium"`);
    await queryRunner.query(`ALTER TABLE "party" DROP COLUMN "leaderId"`);
    await queryRunner.query(`ALTER TABLE "party" DROP COLUMN "updatedAt"`);
    await queryRunner.query(`ALTER TABLE "party" DROP COLUMN "isActive"`);
    await queryRunner.query(`ALTER TABLE "region" DROP COLUMN "updatedAt"`);
    await queryRunner.query(`ALTER TABLE "region" DROP COLUMN "createdAt"`);
    await queryRunner.query(`DROP TABLE "work_session"`);
    await queryRunner.query(`DROP TABLE "factory"`);
    await queryRunner.query(`DROP TYPE "public"."factory_type_enum"`);
  }
}
