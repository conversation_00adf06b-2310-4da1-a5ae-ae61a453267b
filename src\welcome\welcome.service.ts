import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ChatService } from '../chat/chat.service';
import { ChatType } from '../chat/entity/chat.entity';
import { CreateChatDto } from '../chat/dto/create-chat.dto';
import { SendMessageDto } from '../chat/dto/send-message.dto';
import { User } from '../user/entity/user.entity';

@Injectable()
export class WelcomeService {
  private readonly ADMIN_USER_ID = 1; // Admin user ID for sending welcome messages

  constructor(
    @Inject(forwardRef(() => ChatService))
    private readonly chatService: ChatService,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * Send a welcome message to a newly verified user from the admin user
   */
  async sendWelcomeMessage(userId: number): Promise<void> {
    console.log(`Starting to send welcome message to user ${userId} from admin user ${this.ADMIN_USER_ID}`);

    try {
      // First, check if both users exist
      const [adminUser, targetUser] = await Promise.all([
        this.userRepository.findOne({ where: { id: this.ADMIN_USER_ID } }),
        this.userRepository.findOne({ where: { id: userId } }),
      ]);

      if (!adminUser) {
        console.error(`Admin user with ID ${this.ADMIN_USER_ID} not found`);
        return;
      }

      if (!targetUser) {
        console.error(`Target user with ID ${userId} not found`);
        return;
      }

      console.log(`Both users found - Admin: ${adminUser.username}, Target: ${targetUser.username}`);

      const welcomeContent = this.getWelcomeMessageContent();

      // Create or find existing direct chat between admin and the new user
      const createChatDto: CreateChatDto = {
        type: ChatType.DIRECT,
        participantIds: [userId], // Admin will be added automatically by createChat
      };

      const chat = await this.chatService.createChat(createChatDto, this.ADMIN_USER_ID);
      console.log(`Created/found chat ${chat.id} between admin and user ${userId}`);

      // Send welcome message from admin to the new user
      const sendMessageDto: SendMessageDto = {
        content: welcomeContent,
        type: 'text' as any,
      };

      const message = await this.chatService.sendMessage(
        chat.id,
        this.ADMIN_USER_ID,
        sendMessageDto,
      );

      console.log(`Welcome message sent successfully to user ${userId}, message ID: ${message.id}`);
    } catch (error) {
      console.error(`Failed to send welcome message to user ${userId}:`, error);
      console.error('Error stack:', error.stack);
      // Don't throw error to avoid breaking the verification process
    }
  }

  /**
   * Test method to manually send welcome message (for testing purposes)
   */
  async sendTestWelcomeMessage(userId: number): Promise<{ success: boolean; message: string }> {
    try {
      await this.sendWelcomeMessage(userId);
      return { success: true, message: 'Welcome message sent successfully' };
    } catch (error) {
      return { success: false, message: `Failed to send welcome message: ${error.message}` };
    }
  }

  /**
   * Get the welcome message content with helpful links and information
   */
  private getWelcomeMessageContent(): string {
    return `🎉 Welcome to Warfront Nations! 🎉

Congratulations on successfully verifying your account! You're now ready to embark on your journey in the world of strategic warfare and political intrigue.

🚀 **Getting Started:**
• Build your character by training your Strength, Intelligence, and Endurance
• Work at factories to earn money and experience
• Join or create a political party to gain influence
• Participate in regional politics and elections

⚔️ **Warfare & Strategy:**
• Declare wars on other regions to expand your territory
• Participate in revolution wars to change regional leadership
• Use auto-attack features (Premium users only) for continuous warfare
• Travel between regions to explore new opportunities

🏛️ **Political System:**
• Vote in state elections to choose your leaders
• Run for office and lead your state to victory
• Form alliances and manage diplomatic relationships
• Control regions and expand your state's influence

💰 **Economy & Premium Features:**
• Earn gold through various activities
• Purchase premium subscriptions for enhanced features
• Access auto-work and auto-war modes with premium
• Faster energy regeneration and exclusive benefits

📚 **Helpful Resources:**
• Game Wiki: https://wiki.warfront-nations.com/
• Reddit: https://www.reddit.com/r/warfront_nations/
• Community Telegram: https://t.me/warfront_nations
• Support: Reach out to us on Telegram

💡 **Pro Tips:**
• Energy regenerates every 30 minutes (10 for regular, 20 for premium users)
• Join a party early to participate in politics
• Balance training, working, and warfare for optimal growth
• Premium users get significant advantages - consider upgrading!

Good luck, and may your nation prosper! 🏆

---
This is an automated welcome message. If you need help, don't hesitate to reach out to our community or support team.`;
  }
}
