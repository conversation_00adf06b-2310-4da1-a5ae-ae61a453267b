import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1746392620125 implements MigrationInterface {
    name = 'Migrations1746392620125'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "work_session" ADD "efficiencyMultiplier" double precision NOT NULL DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE "work_session" ADD "resourceType" "public"."factory_type_enum"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "work_session" DROP COLUMN "resourceType"`);
        await queryRunner.query(`ALTER TABLE "work_session" DROP COLUMN "efficiencyMultiplier"`);
    }

}
