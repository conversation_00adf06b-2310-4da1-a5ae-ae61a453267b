import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1748468084737 implements MigrationInterface {
    name = 'Migrations1748468084737'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."chat_type_enum" AS ENUM('direct', 'group')`);
        await queryRunner.query(`CREATE TABLE "chat" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "type" "public"."chat_type_enum" NOT NULL DEFAULT 'direct', "name" character varying, "description" character varying, "lastMessageAt" TIMESTAMP, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_9d0b2ba74336710fd31154738a5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."message_type_enum" AS ENUM('text', 'system')`);
        await queryRunner.query(`CREATE TYPE "public"."message_status_enum" AS ENUM('sent', 'delivered', 'read')`);
        await queryRunner.query(`CREATE TABLE "message" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "content" text NOT NULL, "type" "public"."message_type_enum" NOT NULL DEFAULT 'text', "status" "public"."message_status_enum" NOT NULL DEFAULT 'sent', "editedAt" TIMESTAMP, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "chatId" uuid, "senderId" integer, CONSTRAINT "PK_ba01f0a3e0123651915008bc578" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_8051177e63e114cb9e78db7f0b" ON "message" ("senderId", "createdAt") `);
        await queryRunner.query(`CREATE INDEX "IDX_93a28d680f3f131dea7415e0bf" ON "message" ("chatId", "createdAt") `);
        await queryRunner.query(`CREATE TABLE "message_read_status" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "readAt" TIMESTAMP, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "messageId" uuid, "userId" integer, CONSTRAINT "UQ_a86c13229c526af274e48035592" UNIQUE ("messageId", "userId"), CONSTRAINT "PK_258e8d92b4e212a121dc10a74d3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_0f3c243224597f5655042caf58" ON "message_read_status" ("userId", "readAt") `);
        await queryRunner.query(`CREATE TABLE "chat_participants" ("chatId" uuid NOT NULL, "userId" integer NOT NULL, CONSTRAINT "PK_d3101b19215e8540d891f98c065" PRIMARY KEY ("chatId", "userId"))`);
        await queryRunner.query(`CREATE INDEX "IDX_e16675fae83bc603f30ae8fbdd" ON "chat_participants" ("chatId") `);
        await queryRunner.query(`CREATE INDEX "IDX_fb6add83b1a7acc94433d38569" ON "chat_participants" ("userId") `);
        await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" RENAME TO "notification_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."notification_type_enum" AS ENUM('war_declared', 'war_joined', 'war_phase_changed', 'war_ended', 'war_damage_milestone', 'region_conquered', 'resources_captured', 'revolution_succeeded', 'state_election', 'system')`);
        await queryRunner.query(`ALTER TABLE "notification" ALTER COLUMN "type" TYPE "public"."notification_type_enum" USING "type"::"text"::"public"."notification_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."notification_type_enum_old"`);
        await queryRunner.query(`ALTER TABLE "message" ADD CONSTRAINT "FK_619bc7b78eba833d2044153bacc" FOREIGN KEY ("chatId") REFERENCES "chat"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "message" ADD CONSTRAINT "FK_bc096b4e18b1f9508197cd98066" FOREIGN KEY ("senderId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "message_read_status" ADD CONSTRAINT "FK_ab27ff20485b9afa15f7e3d1ca8" FOREIGN KEY ("messageId") REFERENCES "message"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "message_read_status" ADD CONSTRAINT "FK_00956f27e567b20ea63956a94da" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "chat_participants" ADD CONSTRAINT "FK_e16675fae83bc603f30ae8fbdd5" FOREIGN KEY ("chatId") REFERENCES "chat"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "chat_participants" ADD CONSTRAINT "FK_fb6add83b1a7acc94433d385692" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "chat_participants" DROP CONSTRAINT "FK_fb6add83b1a7acc94433d385692"`);
        await queryRunner.query(`ALTER TABLE "chat_participants" DROP CONSTRAINT "FK_e16675fae83bc603f30ae8fbdd5"`);
        await queryRunner.query(`ALTER TABLE "message_read_status" DROP CONSTRAINT "FK_00956f27e567b20ea63956a94da"`);
        await queryRunner.query(`ALTER TABLE "message_read_status" DROP CONSTRAINT "FK_ab27ff20485b9afa15f7e3d1ca8"`);
        await queryRunner.query(`ALTER TABLE "message" DROP CONSTRAINT "FK_bc096b4e18b1f9508197cd98066"`);
        await queryRunner.query(`ALTER TABLE "message" DROP CONSTRAINT "FK_619bc7b78eba833d2044153bacc"`);
        await queryRunner.query(`CREATE TYPE "public"."notification_type_enum_old" AS ENUM('war_declared', 'war_joined', 'war_phase_changed', 'war_ended', 'war_damage_milestone', 'region_conquered', 'resources_captured', 'revolution_succeeded', 'system')`);
        await queryRunner.query(`ALTER TABLE "notification" ALTER COLUMN "type" TYPE "public"."notification_type_enum_old" USING "type"::"text"::"public"."notification_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."notification_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."notification_type_enum_old" RENAME TO "notification_type_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_fb6add83b1a7acc94433d38569"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e16675fae83bc603f30ae8fbdd"`);
        await queryRunner.query(`DROP TABLE "chat_participants"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_0f3c243224597f5655042caf58"`);
        await queryRunner.query(`DROP TABLE "message_read_status"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_93a28d680f3f131dea7415e0bf"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8051177e63e114cb9e78db7f0b"`);
        await queryRunner.query(`DROP TABLE "message"`);
        await queryRunner.query(`DROP TYPE "public"."message_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."message_type_enum"`);
        await queryRunner.query(`DROP TABLE "chat"`);
        await queryRunner.query(`DROP TYPE "public"."chat_type_enum"`);
    }

}
