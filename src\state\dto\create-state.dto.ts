import { IsString, IsUrl, IsOptional, IsBoolean } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateStateDto {
  @ApiProperty({
    description: 'Name of the state',
    example: 'Republic of Azuria',
  })
  @IsString()
  name: string;

  @ApiPropertyOptional({
    description: 'Description or background information about the state',
    example:
      'A democratic republic founded on principles of liberty and prosperity.',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: "URL to the state's flag image",
    example: 'https://example.com/flags/azuria.png',
  })
  @IsOptional()
  flagUrl?: string;

  @ApiPropertyOptional({
    description: "Whether to include the leader's region as part of the state",
    default: true,
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  includeLeaderRegion?: boolean = true;
}
