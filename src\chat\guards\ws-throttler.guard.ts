import { CanActivate, ExecutionContext, Injectable, Logger } from '@nestjs/common';
import { WsException } from '@nestjs/websockets';
import { Socket } from 'socket.io';

interface AuthenticatedSocket extends Socket {
  userId?: number;
  username?: string;
}

@Injectable()
export class WsThrottlerGuard implements CanActivate {
  private readonly logger = new Logger(WsThrottlerGuard.name);
  private readonly messageTimestamps = new Map<number, number[]>(); // userId -> timestamps
  private readonly MESSAGE_LIMIT = 10; // messages per minute
  private readonly TIME_WINDOW = 60 * 1000; // 1 minute in milliseconds
  private readonly CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes
  private cleanupTimer: NodeJS.Timeout;

  constructor() {
    // Start periodic cleanup to prevent memory leaks
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredEntries();
    }, this.CLEANUP_INTERVAL);
  }

  canActivate(context: ExecutionContext): boolean {
    const client: AuthenticatedSocket = context.switchToWs().getClient();
    const userId = client.userId;

    if (!userId) {
      return true; // Let auth guard handle this
    }

    const now = Date.now();
    const userTimestamps = this.messageTimestamps.get(userId) || [];

    // Remove timestamps older than the time window
    const recentTimestamps = userTimestamps.filter(
      timestamp => now - timestamp < this.TIME_WINDOW
    );

    if (recentTimestamps.length >= this.MESSAGE_LIMIT) {
      this.logger.warn(
        `Rate limit exceeded for user ${userId}. ${recentTimestamps.length} messages in the last minute.`
      );
      throw new WsException('Rate limit exceeded. Please slow down.');
    }

    // Add current timestamp and update the map
    recentTimestamps.push(now);
    this.messageTimestamps.set(userId, recentTimestamps);

    return true;
  }

  // Clean up user data when they disconnect
  cleanupUser(userId: number): void {
    this.messageTimestamps.delete(userId);
    this.logger.debug(`Cleaned up rate limiting data for user ${userId}`);
  }

  // Periodic cleanup of expired entries
  private cleanupExpiredEntries(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [userId, timestamps] of this.messageTimestamps.entries()) {
      const recentTimestamps = timestamps.filter(
        timestamp => now - timestamp < this.TIME_WINDOW
      );

      if (recentTimestamps.length === 0) {
        this.messageTimestamps.delete(userId);
        cleanedCount++;
      } else if (recentTimestamps.length < timestamps.length) {
        this.messageTimestamps.set(userId, recentTimestamps);
      }
    }

    if (cleanedCount > 0) {
      this.logger.debug(`Cleaned up rate limiting data for ${cleanedCount} inactive users`);
    }
  }

  // Cleanup on module destroy
  onModuleDestroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.messageTimestamps.clear();
  }
}
