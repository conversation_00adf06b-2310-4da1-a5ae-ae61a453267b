import { RegionModule } from 'src/region/region.module';
import { PartyController } from './party.controller';
import { PartyService } from './party.service';
import { Module } from '@nestjs/common';
import { Party } from './entity/party.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserModule } from 'src/user/user.module';
import { PartyJoinRequest } from './entity/party-join-request.entity';
import { PartyJoinRequestController } from './party-join-request.controller';

@Module({
  imports: [TypeOrmModule.forFeature([Party,PartyJoinRequest]), RegionModule, UserModule],
  controllers: [PartyController, PartyJoinRequestController],
  providers: [PartyService],
})
export class PartyModule {}
