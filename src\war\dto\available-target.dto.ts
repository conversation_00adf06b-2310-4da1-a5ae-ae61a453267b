import { ApiProperty } from '@nestjs/swagger';

export class AvailableTargetDto {
  @ApiProperty({
    description: 'Region ID',
    example: 'uuid-string',
  })
  id: string;

  @ApiProperty({
    description: 'Region name',
    example: 'California',
  })
  name: string;

  @ApiProperty({
    description: 'Country code',
    example: 'US-CA',
  })
  countryCode: string;

  @ApiProperty({
    description: 'Distance from attacking region in kilometers (for sea wars)',
    example: 850.5,
    required: false,
  })
  distance?: number;

  @ApiProperty({
    description: 'Whether the region has sea access',
    example: true,
  })
  seaAccess: boolean;

  @ApiProperty({
    description: 'Region population',
    example: 1250,
  })
  population: number;

  @ApiProperty({
    description: 'State information',
    required: false,
  })
  state?: {
    id: string;
    name: string;
  };
}
