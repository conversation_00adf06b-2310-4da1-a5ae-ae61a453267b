import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WarReport } from './entity/war-report.entity';
import { War, WarStatus, WarType, WarTarget } from './entity/war.entity';
import { User } from '../user/entity/user.entity';
import { WarService } from './war.service';

@Injectable()
export class WarReportService {
  constructor(
    @InjectRepository(War)
    private warRepository: Repository<War>,
    @InjectRepository(WarReport)
    private warReportRepository: Repository<WarReport>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private warService: WarService, // Inject WarService
  ) {}

  async generateWarReport(war: War): Promise<WarReport> {
    if (war.status !== WarStatus.ENDED) {
      throw new Error('Cannot generate report for a war that has not ended');
    }

    const warReport = new WarReport();
    warReport.warId = war.id;

    const attackerName =
      war.attackerState?.name || war.attackerRegion?.name || 'Unknown';
    const defenderName =
      war.defenderState?.name || war.defenderRegion?.name || 'Unknown';
    warReport.title = `War between ${attackerName} and ${defenderName}`;

    warReport.attackerName = attackerName;
    warReport.defenderName = defenderName;
    warReport.winner = this.warService.determineWarWinner(war);

    // Generate a summary
    const attackerWon =
      war.attackerGroundDamage >= war.damageRequirement &&
      war.attackerGroundDamage > war.defenderGroundDamage;

    let summary = `A ${this.getWarTypeDescription(war.warType)} war that `;

    if (attackerWon) {
      summary += `resulted in a victory for ${attackerName} against ${defenderName}. `;

      if (war.warTarget === WarTarget.CONQUEST && war.targetRegion) {
        summary += `${attackerName} successfully conquered ${war.targetRegion.name}. `;
      } else if (war.warTarget === WarTarget.RESOURCES) {
        summary += `${attackerName} successfully captured resources from ${defenderName}. `;
      } else if (war.warType === WarType.REVOLUTION) {
        summary += `The revolution in ${defenderName} succeeded. `;
      }
    } else {
      summary += `ended with ${defenderName} successfully defending against ${attackerName}. `;
    }

    // Add damage statistics to summary
    summary += `The attackers dealt ${war.attackerGroundDamage.toFixed(0)} ground damage and ${war.attackerSeaDamage.toFixed(0)} sea damage, `;
    summary += `while the defenders dealt ${war.defenderGroundDamage.toFixed(0)} ground damage and ${war.defenderSeaDamage.toFixed(0)} sea damage.`;

    warReport.summary = summary;
    warReport.warType = war.warType;
    warReport.warTarget = war.warTarget;
    warReport.startDate = war.startedAt;
    warReport.endDate = war.endedAt;

    // Calculate duration in hours
    if (war.startedAt && war.endedAt) {
      const durationMs = war.endedAt.getTime() - war.startedAt.getTime();
      warReport.durationHours = durationMs / (1000 * 60 * 60);
    } else {
      warReport.durationHours = 0;
    }

    // Calculate battle statistics
    const attackerParticipants = war.participants?.attackers?.length || 0;
    const defenderParticipants = war.participants?.defenders?.length || 0;

    // Calculate total energy spent (this is an estimate as we don't store energy spent per participant)
    // In a real implementation, you would track energy spent per participant
    const attackerEnergySpent = Math.round(
      (war.attackerGroundDamage + war.attackerSeaDamage) / 100,
    );
    const defenderEnergySpent = Math.round(
      (war.defenderGroundDamage + war.defenderSeaDamage) / 100,
    );
    const totalEnergySpent = attackerEnergySpent + defenderEnergySpent;

    // Calculate efficiency (damage per energy)
    const attackerEfficiency =
      attackerEnergySpent > 0
        ? (war.attackerGroundDamage + war.attackerSeaDamage) /
          attackerEnergySpent
        : 0;

    const defenderEfficiency =
      defenderEnergySpent > 0
        ? (war.defenderGroundDamage + war.defenderSeaDamage) /
          defenderEnergySpent
        : 0;

    warReport.battleStatistics = {
      totalAttackerDamage: war.attackerGroundDamage + war.attackerSeaDamage,
      totalDefenderDamage: war.defenderGroundDamage + war.defenderSeaDamage,
      attackerParticipants,
      defenderParticipants,
      attackerEfficiency,
      defenderEfficiency,
      totalEnergySpent,
      attackerEnergySpent,
      defenderEnergySpent,
    };

    // Generate timeline
    warReport.timeline = await this.generateTimeline(war);

    // Get top damage dealers
    warReport.topDamageDealers = await this.getTopDamageDealers(war);

    // Generate outcome
    warReport.outcome = await this.generateOutcome(war, attackerWon);

    // Generate visual data (placeholder URLs - in a real implementation, you would generate actual charts)
    warReport.visualData = {
      mapUrl: `https://example.com/war-maps/${war.id}`,
      damageChartUrl: `https://example.com/war-charts/${war.id}/damage`,
      participationChartUrl: `https://example.com/war-charts/${war.id}/participation`,
    };

    return this.warReportRepository.save(warReport);
  }

  private getWarTypeDescription(warType: string): string {
    switch (warType) {
      case 'GROUND':
        return 'ground-based';
      case 'SEA':
        return 'naval';
      case 'REVOLUTION':
        return 'revolutionary';
      default:
        return 'military';
    }
  }

  private async generateTimeline(war: War): Promise<any[]> {
    const timeline: Array<{
      timestamp: Date;
      description: string;
      damage?: number;
      side?: 'attacker' | 'defender';
      eventType: string;
    }> = [];

    // Add war declaration
    if (war.declaredAt) {
      timeline.push({
        timestamp: war.declaredAt,
        description: `War declared by ${war.declaredBy?.username}`,
        eventType: 'declaration',
      });
    }

    // Add war start
    if (war.startedAt) {
      timeline.push({
        timestamp: war.startedAt,
        description: 'War officially began',
        eventType: 'start',
      });
    }

    // Add sea phase events
    if (war.seaPhaseStartedAt) {
      timeline.push({
        timestamp: war.seaPhaseStartedAt,
        description: 'Sea phase began',
        eventType: 'sea_phase_start',
      });
    }

    if (war.seaPhaseEndedAt) {
      const attackerWonSea = war.attackerSeaDamage > war.defenderSeaDamage;
      timeline.push({
        timestamp: war.seaPhaseEndedAt,
        description: `Sea phase ended. ${attackerWonSea ? 'Attackers' : 'Defenders'} won the sea phase.`,
        eventType: 'sea_phase_end',
      });
    }

    // Add ground phase events
    if (war.groundPhaseStartedAt) {
      timeline.push({
        timestamp: war.groundPhaseStartedAt,
        description: 'Ground phase began',
        eventType: 'ground_phase_start',
      });
    }

    // Add significant battle events
    if (war.battleEvents) {
      // Sort battle events by damage
      const sortedEvents = [...war.battleEvents].sort(
        (a, b) => (b.damage || 0) - (a.damage || 0),
      );

      // Take top 10 damage events
      const topDamageEvents = sortedEvents.slice(0, 10);

      for (const event of topDamageEvents) {
        if (event.damage && event.userId) {
          const user = await this.userRepository.findOne({
            where: { id: event.userId },
          });

          timeline.push({
            timestamp: event.timestamp,
            description: `${user?.username || 'Unknown user'} dealt ${event.damage.toFixed(0)} damage for the ${event.side} side`,
            damage: event.damage,
            side: event.side,
            eventType: 'battle',
          });
        }
      }
    }

    // Add war end
    if (war.endedAt) {
      const attackerWon =
        war.attackerGroundDamage >= war.damageRequirement &&
        war.attackerGroundDamage > war.defenderGroundDamage;

      timeline.push({
        timestamp: war.endedAt,
        description: `War ended. ${attackerWon ? 'Attackers' : 'Defenders'} emerged victorious.`,
        eventType: 'end',
      });
    }

    // Sort by timestamp
    return timeline.sort(
      (a, b) => a.timestamp.getTime() - b.timestamp.getTime(),
    );
  }

  private async getTopDamageDealers(war: War): Promise<any[]> {
    const topDealers: Array<{
      userId: number;
      username: string;
      damage: number;
      energySpent: number;
      efficiency: number;
      side: 'attacker' | 'defender';
    }> = [];

    // Process attackers
    const attackers = war.participants?.attackers || [];
    for (const attacker of attackers) {
      const user = await this.userRepository.findOne({
        where: { id: attacker.userId },
      });
      if (user) {
        // Estimate energy spent based on damage
        const energySpent = Math.round(attacker.damage / 100);
        const efficiency = energySpent > 0 ? attacker.damage / energySpent : 0;

        topDealers.push({
          userId: user.id,
          username: user.username,
          damage: attacker.damage,
          energySpent,
          efficiency,
          side: 'attacker',
        });
      }
    }

    // Process defenders
    const defenders = war.participants?.defenders || [];
    for (const defender of defenders) {
      const user = await this.userRepository.findOne({
        where: { id: defender.userId },
      });
      if (user) {
        // Estimate energy spent based on damage
        const energySpent = Math.round(defender.damage / 100);
        const efficiency = energySpent > 0 ? defender.damage / energySpent : 0;

        topDealers.push({
          userId: user.id,
          username: user.username,
          damage: defender.damage,
          energySpent,
          efficiency,
          side: 'defender',
        });
      }
    }

    // Sort by damage and take top 10
    return topDealers.sort((a, b) => b.damage - a.damage).slice(0, 10);
  }

  private async generateOutcome(war: War, attackerWon: boolean): Promise<any> {
    const outcome: any = {
      description: '',
    };

    const attackerName =
      war.attackerState?.name || war.attackerRegion?.name || 'Unknown';
    const defenderName =
      war.defenderState?.name || war.defenderRegion?.name || 'Unknown';

    if (attackerWon) {
      outcome.description = `${attackerName} emerged victorious against ${defenderName}. `;

      if (war.warTarget === WarTarget.CONQUEST && war.targetRegion) {
        outcome.description += `${attackerName} successfully conquered ${war.targetRegion.name}.`;
        outcome.territoryChanged = true;
        outcome.regionTransferred = war.targetRegion.name;
      } else if (war.warTarget === WarTarget.RESOURCES && war.resourcesTarget) {
        outcome.description += `${attackerName} successfully captured resources from ${defenderName}.`;
        outcome.territoryChanged = false;
        outcome.resourcesCaptured = war.resourcesTarget;
      } else if (war.warType === WarType.REVOLUTION) {
        outcome.description += `The revolution in ${defenderName} succeeded, resulting in a change of leadership.`;
        outcome.territoryChanged = false;
        outcome.leadershipChanged = true;
        // In a real implementation, you would store the new leader's name
        outcome.newLeader = 'New Leader';
      }
    } else {
      outcome.description = `${defenderName} successfully defended against ${attackerName}'s attack.`;
      outcome.territoryChanged = false;
    }

    return outcome;
  }

  async findAllWarReports(): Promise<WarReport[]> {
    return this.warReportRepository.find({
      order: { createdAt: 'DESC' },
    });
  }

  async findWarReportById(id: string): Promise<WarReport> {
    const result = await this.warReportRepository.findOne({ where: { id } });
    if (!result) {
      throw new Error(`War report with ID ${id} not found`);
    }
    return result;
  }

  async findWarReportByWarId(warId: string): Promise<WarReport> {
    const result = await this.warReportRepository.findOne({ where: { warId } });
    if (!result) {
      throw new Error(`War report with war ID ${warId} not found`);
    }
    return result;
  }

  async findWarReportsByRegion(regionName: string): Promise<WarReport[]> {
    return this.warReportRepository.find({
      where: [{ attackerName: regionName }, { defenderName: regionName }],
      order: { createdAt: 'DESC' },
    });
  }

  async findWarReportsByState(stateName: string): Promise<WarReport[]> {
    return this.warReportRepository.find({
      where: [{ attackerName: stateName }, { defenderName: stateName }],
      order: { createdAt: 'DESC' },
    });
  }

  async getTopWarriors(limit: number = 10): Promise<any[]> {
    const allReports = await this.warReportRepository.find();
    const warriorStats = new Map();

    // Collect stats from all reports
    for (const report of allReports) {
      for (const warrior of report.topDamageDealers) {
        if (!warriorStats.has(warrior.userId)) {
          warriorStats.set(warrior.userId, {
            userId: warrior.userId,
            username: warrior.username,
            totalDamage: 0,
            totalEnergySpent: 0,
            warCount: 0,
            wins: 0,
            losses: 0,
          });
        }

        const stats = warriorStats.get(warrior.userId);
        stats.totalDamage += warrior.damage;
        stats.totalEnergySpent += warrior.energySpent;
        stats.warCount += 1;

        // Count wins and losses
        if (
          (warrior.side === 'attacker' && report.winner === 'attacker') ||
          (warrior.side === 'defender' && report.winner === 'defender')
        ) {
          stats.wins += 1;
        } else {
          stats.losses += 1;
        }
      }
    }

    // Calculate efficiency and win rate
    for (const stats of warriorStats.values()) {
      stats.efficiency =
        stats.totalEnergySpent > 0
          ? stats.totalDamage / stats.totalEnergySpent
          : 0;
      stats.winRate =
        stats.warCount > 0 ? (stats.wins / stats.warCount) * 100 : 0;
    }

    // Sort by total damage and take top N
    return Array.from(warriorStats.values())
      .sort((a, b) => b.totalDamage - a.totalDamage)
      .slice(0, limit);
  }

  async getMostEfficientWarriors(limit: number = 10): Promise<any[]> {
    const allReports = await this.warReportRepository.find();
    const warriorStats = new Map();

    // Collect stats from all reports
    for (const report of allReports) {
      for (const warrior of report.topDamageDealers) {
        if (!warriorStats.has(warrior.userId)) {
          warriorStats.set(warrior.userId, {
            userId: warrior.userId,
            username: warrior.username,
            totalDamage: 0,
            totalEnergySpent: 0,
            warCount: 0,
          });
        }

        const stats = warriorStats.get(warrior.userId);
        stats.totalDamage += warrior.damage;
        stats.totalEnergySpent += warrior.energySpent;
        stats.warCount += 1;
      }
    }

    // Calculate efficiency and filter out warriors with less than 3 wars
    const efficientWarriors = Array.from(warriorStats.values())
      .filter((stats) => stats.warCount >= 3 && stats.totalEnergySpent > 0)
      .map((stats) => ({
        ...stats,
        efficiency: stats.totalDamage / stats.totalEnergySpent,
      }));

    // Sort by efficiency and take top N
    return efficientWarriors
      .sort((a, b) => b.efficiency - a.efficiency)
      .slice(0, limit);
  }
}
