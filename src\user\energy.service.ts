import { Injectable, Logger, forwardRef, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entity/user.entity';
import { Cron } from '@nestjs/schedule';

@Injectable()
export class EnergyService {
  private readonly logger = new Logger(EnergyService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * Update energy for a single user
   * @param user The user to update energy for
   * @returns The updated user
   */
  async updateUserEnergy(user: User): Promise<User> {
    user.updateEnergy();
    return this.userRepository.save(user);
  }

  /**
   * Update energy for a user by ID
   * @param userId The ID of the user to update energy for
   * @returns The updated user
   */
  async updateEnergyById(userId: number): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id: userId }, relations: ['region','region.state', 'workingAt'] });
    if (!user) {
      throw new Error(`User with ID ${userId} not found`);
    }

    return this.updateUserEnergy(user);
  }

  /**
   * Batch update energy for all active users
   * This is run as a scheduled task every 30 minutes
   */
  @Cron('0 */30 * * * *') // Run every 30 minutes
  async batchUpdateEnergy(): Promise<void> {
    this.logger.log('Running batch energy update for all users');

    try {
      // Get all active users
      const users = await this.userRepository.find({
        where: { isActive: true },
      });

      this.logger.log(`Updating energy for ${users.length} active users`);

      // Update energy for each user
      for (const user of users) {
        user.updateEnergy();
      }

      // Save all users in a single batch operation
      await this.userRepository.save(users);

      this.logger.log('Batch energy update completed successfully');
    } catch (error) {
      this.logger.error(
        `Error in batch energy update: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Calculate the current energy for a user without saving
   * This is useful for getting the current energy without updating the database
   * @param user The user to calculate energy for
   * @returns The calculated current energy
   */
  calculateCurrentEnergy(user: User): number {
    const now = new Date();
    const lastUpdate = user.updatedAt || user.createdAt;

    // Calculate time passed in 30-minute intervals
    const halfHoursPassed =
      (now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 30);

    // Energy regeneration rate: 10 per 30 minutes for non-premium, 20 per 30 minutes for premium
    const energyRegen = Math.floor(
      halfHoursPassed * (user.isPremium ? 20 : 10),
    );
    const maxEnergy = user.isPremium ? 200 : 100;

    return Math.min(maxEnergy, user.energy + energyRegen);
  }
}
