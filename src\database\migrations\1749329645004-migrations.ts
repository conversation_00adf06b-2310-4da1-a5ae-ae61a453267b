import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1749329645004 implements MigrationInterface {
    name = 'Migrations1749329645004'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Step 1: Add 'active' value to existing enums first (if not exists)
        try {
            await queryRunner.query(`ALTER TYPE "public"."war_status_enum" ADD VALUE 'active'`);
        } catch (e) {
            // Value might already exist, continue
        }

        try {
            await queryRunner.query(`ALTER TYPE "public"."war_history_finalstatus_enum" ADD VALUE 'active'`);
        } catch (e) {
            // Value might already exist, continue
        }

        // Step 2: Recreate war_status_enum with only needed values using CASE mapping
        await queryRunner.query(`ALTER TYPE "public"."war_status_enum" RENAME TO "war_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."war_status_enum" AS ENUM('pending', 'active', 'ended')`);
        await queryRunner.query(`ALTER TABLE "war" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`
            ALTER TABLE "war" ALTER COLUMN "status" TYPE "public"."war_status_enum"
            USING CASE
                WHEN "status"::text IN ('ground_phase', 'sea_phase', 'revolution_phase') THEN 'active'::war_status_enum
                WHEN "status"::text = 'pending' THEN 'pending'::war_status_enum
                WHEN "status"::text = 'ended' THEN 'ended'::war_status_enum
                WHEN "status"::text = 'active' THEN 'active'::war_status_enum
                ELSE 'pending'::war_status_enum
            END
        `);
        await queryRunner.query(`ALTER TABLE "war" ALTER COLUMN "status" SET DEFAULT 'pending'`);
        await queryRunner.query(`DROP TYPE "public"."war_status_enum_old"`);

        // Step 3: Recreate war_history_finalstatus_enum with only needed values using CASE mapping
        await queryRunner.query(`ALTER TYPE "public"."war_history_finalstatus_enum" RENAME TO "war_history_finalstatus_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."war_history_finalstatus_enum" AS ENUM('pending', 'active', 'ended')`);
        await queryRunner.query(`
            ALTER TABLE "war_history" ALTER COLUMN "finalStatus" TYPE "public"."war_history_finalstatus_enum"
            USING CASE
                WHEN "finalStatus"::text IN ('ground_phase', 'sea_phase', 'revolution_phase') THEN 'active'::war_history_finalstatus_enum
                WHEN "finalStatus"::text = 'pending' THEN 'pending'::war_history_finalstatus_enum
                WHEN "finalStatus"::text = 'ended' THEN 'ended'::war_history_finalstatus_enum
                WHEN "finalStatus"::text = 'active' THEN 'active'::war_history_finalstatus_enum
                ELSE 'pending'::war_history_finalstatus_enum
            END
        `);
        await queryRunner.query(`DROP TYPE "public"."war_history_finalstatus_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."war_history_finalstatus_enum_old" AS ENUM('pending', 'ground_phase', 'revolution_phase', 'ended')`);
        await queryRunner.query(`ALTER TABLE "war_history" ALTER COLUMN "finalStatus" TYPE "public"."war_history_finalstatus_enum_old" USING "finalStatus"::"text"::"public"."war_history_finalstatus_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."war_history_finalstatus_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."war_history_finalstatus_enum_old" RENAME TO "war_history_finalstatus_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."war_status_enum_old" AS ENUM('revolution_phase', 'pending', 'ground_phase', 'ended')`);
        await queryRunner.query(`ALTER TABLE "war" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "war" ALTER COLUMN "status" TYPE "public"."war_status_enum_old" USING "status"::"text"::"public"."war_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "war" ALTER COLUMN "status" SET DEFAULT 'pending'`);
        await queryRunner.query(`DROP TYPE "public"."war_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."war_status_enum_old" RENAME TO "war_status_enum"`);
    }

}
