import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as Minio from 'minio';

@Injectable()
export class StorageService {
  private readonly logger = new Logger(StorageService.name);
  private readonly minioClient: Minio.Client;
  private readonly bucketName: string;
  private readonly endpoint: string;
  private readonly region: string;

  constructor(private readonly configService: ConfigService) {
    this.bucketName = this.configService.get<string>('S3_BUCKET') || 'warfront';
    this.region = this.configService.get<string>('S3_REGION') || 'fsn1';
    const endpoint = this.configService.get<string>('S3_ENDPOINT') || 'https://fsn1.your-objectstorage.com';
    const accessKey = this.configService.get<string>('S3_ACCESS_KEY');
    const secretKey = this.configService.get<string>('S3_SECRET_KEY');

    if (!this.region || !accessKey || !secretKey) {
      throw new Error('Missing required S3 configuration');
    }

    // Store the full endpoint for URL generation

    // Initialize MinIO client
    this.minioClient = new Minio.Client({
      endPoint: endpoint,
      port: 443,
      accessKey: accessKey,
      secretKey: secretKey,
      region: this.region,
    });

    this.logger.log(`MinIO client initialized for region: ${this.region}`);
    this.logger.log(`Full endpoint for URLs: ${this.endpoint}`);
    this.logger.log(`Bucket: ${this.bucketName}`);
    this.logger.log(`Raw S3_BUCKET config: ${this.configService.get<string>('S3_BUCKET')}`);
    this.logger.log(`Raw S3_REGION config: ${this.configService.get<string>('S3_REGION')}`);
  }

  /**
   * Upload a file to Object Storage using MinIO
   */
  async uploadFile(
    key: string,
    buffer: Buffer,
    contentType: string,
    metadata?: Record<string, string>
  ): Promise<string> {
    try {
      this.logger.log(`Attempting to upload file: ${key} to bucket: ${this.bucketName}`);
      this.logger.log(`Endpoint: ${this.endpoint}`);

      // Upload using MinIO client
      const uploadInfo = await this.minioClient.putObject(
        this.bucketName,
        key,
        buffer,
        buffer.length,
        {
          'Content-Type': contentType,
          ...metadata,
        }
      );

      this.logger.log(`Upload successful:`, uploadInfo);

      // Return the public URL using virtual-hosted-style format
      const publicUrl = this.generateVirtualHostedUrl(key);

      this.logger.log(`File uploaded successfully: ${key}`);
      return publicUrl;
    } catch (error) {
      this.logger.error(`Failed to upload file ${key}:`, error);
      this.logger.error(`Error details:`, {
        message: error.message,
        code: error.code,
      });
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  /**
   * Delete a file from Object Storage using MinIO
   */
  async deleteFile(key: string): Promise<void> {
    try {
      await this.minioClient.removeObject(this.bucketName, key);
      this.logger.log(`File deleted successfully: ${key}`);
    } catch (error) {
      this.logger.error(`Failed to delete file ${key}:`, error);
      throw new Error(`Failed to delete file: ${error.message}`);
    }
  }

  /**
   * Generate a presigned URL for temporary access to a private file using MinIO
   */
  async getPresignedUrl(key: string, expiresIn: number = 3600): Promise<string> {
    try {
      const signedUrl = await this.minioClient.presignedGetObject(
        this.bucketName,
        key,
        expiresIn
      );
      return signedUrl;
    } catch (error) {
      this.logger.error(`Failed to generate presigned URL for ${key}:`, error);
      throw new Error(`Failed to generate presigned URL: ${error.message}`);
    }
  }

  /**
   * Extract the key from a full URL
   * Format: https://<bucket>.<region>.your-objectstorage.com/<filename>
   */
  extractKeyFromUrl(url: string): string | null {
    try {
      // The endpoint already includes the bucket: https://bucket.region.your-objectstorage.com
      const prefix = `https://${this.bucketName}.${this.region}.your-objectstorage.com/`;

      if (url.startsWith(prefix)) {
        return url.substring(prefix.length);
      }

      return null;
    } catch (error) {
      this.logger.error(`Failed to extract key from URL ${url}:`, error);
      return null;
    }
  }

  /**
   * Generate a unique file key for storage
   */
  generateFileKey(userId: number, originalName: string, prefix: string = 'avatars'): string {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = originalName.split('.').pop()?.toLowerCase() || 'jpg';

    return `${prefix}/${userId}/${timestamp}-${randomString}.${extension}`;
  }

  /**
   * Generate virtual-hosted-style URL
   * Format: https://<bucket>.<region>.your-objectstorage.com/<filename>
   */
  private generateVirtualHostedUrl(key: string): string {
    // Build the URL like: https://<bucket>.<region>.your-objectstorage.com/<key>
    const virtualHostedUrl = `https://${this.bucketName}.${this.region}.your-objectstorage.com/${key}`;

    this.logger.log(`Generated virtual-hosted URL: ${virtualHostedUrl}`);
    return virtualHostedUrl;
  }
}
