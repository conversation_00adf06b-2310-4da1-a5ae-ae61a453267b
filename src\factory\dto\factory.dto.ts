import {
  <PERSON>String,
  IsEnum,
  IsNumber,
  Min,
  IsNotEmpty,
  IsOptional,
  IsUUID,
  IsBoolean,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { FactoryType } from '../entity/factory.entity';

export class CreateFactoryDto {
  @ApiProperty({ description: 'Name of the factory' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Type of factory', enum: FactoryType })
  @IsEnum(FactoryType)
  type: FactoryType;

  @ApiProperty({ description: 'ID of the region where the factory is located' })
  @IsUUID()
  regionId: string;

  @ApiProperty({ description: 'Wage paid to workers per work session' })
  @IsNumber()
  @Min(0)
  wage: number;

  @ApiProperty({ description: 'Amount of resources produced per work session' })
  @IsNumber()
  @Min(0)
  resourcePerWork: number;

  @ApiProperty({ description: 'Energy cost per work session' })
  @IsNumber()
  @Min(0)
  energyCost: number;

  @ApiProperty({
    description: 'Maximum number of workers allowed in the factory',
  })
  @IsNumber()
  @Min(1)
  maxWorkers: number;

  @ApiProperty({
    description: 'Current number of workers in the factory',
    required: false,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  currentWorkers?: number;
}

export class UpdateFactoryDto {
  @ApiProperty({ description: 'Name of the factory', required: false })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Type of factory',
    enum: FactoryType,
    required: false,
  })
  @IsEnum(FactoryType)
  @IsOptional()
  type?: FactoryType;

  @ApiProperty({
    description: 'ID of the region where the factory is located',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  regionId?: string;

  @ApiProperty({
    description: 'Wage paid to workers per work session',
    required: false,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  wage?: number;

  @ApiProperty({
    description: 'Amount of resources produced per work session',
    required: false,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  resourcePerWork?: number;

  @ApiProperty({ description: 'Energy cost per work session', required: false })
  @IsNumber()
  @Min(0)
  @IsOptional()
  energyCost?: number;

  @ApiProperty({
    description: 'Maximum number of workers allowed in the factory',
    required: false,
  })
  @IsNumber()
  @Min(1)
  @IsOptional()
  maxWorkers?: number;

  @ApiProperty({
    description: 'Current number of workers in the factory',
    required: false,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  currentWorkers?: number;
}

export class WorkAtFactoryDto {
  @ApiProperty({
    description: 'Amount of energy to spend working. Must be at least the factory\'s energyCost.',
    minimum: 1
  })
  @IsNumber()
  @Min(1)
  @IsNotEmpty()
  energySpent: number;
}

export class AutoWorkDto {
  @ApiProperty({
    description: 'Whether to enable or disable auto work mode',
    type: Boolean
  })
  @IsBoolean()
  @IsNotEmpty()
  enable: boolean;
}
