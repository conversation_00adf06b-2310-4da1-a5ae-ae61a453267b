import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from '../../user/entity/user.entity';
import { Factory, FactoryType } from '../../factory/entity/factory.entity';

@Entity()
export class WorkSession {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  factoryId: number;

  @Column()
  workerId: number;

  @Column('float')
  energySpent: number;

  @Column('float')
  wageEarned: number;

  @Column('float')
  resourceEarned: number;

  @Column('float', { default: 1.0 })
  efficiencyMultiplier: number;
  
  @Column({
    type: 'enum',
    enum: FactoryType,
    enumName: 'factory_type_enum',
    nullable: true
  })
  resourceType: FactoryType;

  @ManyToOne(() => Factory, (factory) => factory.workSessions)
  factory: Factory;

  @ManyToOne(() => User, (user) => user.workSessions)
  worker: User;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
