import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { PaymentController } from './payment.controller';
import { PaymentService } from './payment.service';
import { UserModule } from '../user/user.module';
import { PaymentTransaction } from './entity/payment-transaction.entity';
import { GoldPurchase } from './entity/gold-purchase.entity';
import { StripeService } from './stripe.service';
import { MailModule } from '../mail/mail.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([PaymentTransaction, GoldPurchase]),
    ConfigModule,
    UserModule,
    MailModule,
  ],
  controllers: [PaymentController],
  providers: [PaymentService, StripeService],
  exports: [PaymentService, StripeService],
})
export class PaymentModule {}
