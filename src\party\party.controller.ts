import {
  <PERSON>,
  Post,
  Body,
  Get,
  Param,
  Patch,
  Req,
  ParseIntPipe,
  Delete,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiResponse } from '@nestjs/swagger';
import { PartyService } from './party.service';
import { CreatePartyDto } from './dto/create-party.dto';
import { UpdatePartyDto } from './dto/update-party.dto';
import { Request } from 'express';
import { Party } from './entity/party.entity';

@ApiTags('Party')
@Controller('party')
export class PartyController {
  constructor(private readonly partyService: PartyService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new party' })
  async createParty(
    @Body() createPartyDto: CreatePartyDto,
    @Req() req: Request & { user: { userId: number } },
  ) {
    const userId = req.user.userId;
    return this.partyService.createParty(createPartyDto, userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get party details by ID' })
  async getParty(@Param('id') id: string) {
    return this.partyService.getPartyById(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update party details (leader only)' })
  async updateParty(
    @Param('id') id: string,
    @Body() updatePartyDto: UpdatePartyDto,
    @Req() req: Request & { user: { userId: number } },
  ) {
    const userId = req.user.userId;
    return this.partyService.updateParty(id, updatePartyDto, userId);
  }

  @Post(':id/join')
  @ApiOperation({ summary: 'Join a party' })
  async joinParty(
    @Param('id') id: string,
    @Req() req: Request & { user: { userId: number } },
  ) {
    const userId = req.user.userId;
    return this.partyService.joinParty(id, userId);
  }

  @Post(':id/leave')
  @ApiOperation({ summary: 'Leave a party' })
  async leaveParty(
    @Param('id') id: string,
    @Req() req: Request & { user: { userId: number } },
  ) {
    const userId = req.user.userId;
    return this.partyService.leaveParty(id, userId);
  }

  @Post(':id/transfer-leadership/:newLeaderId')
  @ApiOperation({ summary: 'Transfer party leadership to another member' })
  @ApiParam({ name: 'id', description: 'Party ID' })
  @ApiParam({ name: 'newLeaderId', description: 'User ID of the new leader' })
  @ApiResponse({
    status: 200,
    description: 'Leadership transferred successfully',
  })
  @ApiResponse({
    status: 403,
    description: 'Only the current party leader can transfer leadership',
  })
  @ApiResponse({ status: 404, description: 'Party or user not found' })
  @ApiResponse({
    status: 409,
    description: 'The new leader is already leading another party',
  })
  async transferLeadership(
    @Param('id') id: string,
    @Param('newLeaderId', ParseIntPipe) newLeaderId: number,
    @Req() req: Request & { user: { userId: number } },
  ) {
    const userId = req.user.userId;
    return this.partyService.transferLeadership(id, userId, newLeaderId);
  }

  @Get('region/:regionId')
  @ApiOperation({ summary: 'Get parties by region' })
  async getPartiesByRegion(@Param('regionId') regionId: string) {
    return this.partyService.getPartiesByRegion(regionId);
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get parties by user' })
  async getPartiesByUser(@Param('userId', ParseIntPipe) userId: number) {
    return this.partyService.getPartiesByUser(userId);
  }
   @Delete(':id/kick/:memberId')
  kickMember(
    @Param('id') id: string,
    @Param('memberId') memberId: string,
    @Req() req: Request & { user: { userId: number } },
  ): Promise<Party> {
    return this.partyService.kickMember(
      id,
      parseInt(memberId),
      req.user.userId,
    );
  }
}
