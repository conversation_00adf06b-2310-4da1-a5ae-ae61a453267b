import { ApiProperty } from '@nestjs/swagger';
import { NotificationType } from '../entity/notification.entity';
import { UserResponseDto } from '../../user/dto/user-response.dto';

export class NotificationResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the notification',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'User who should receive the notification',
    type: () => UserResponseDto,
  })
  user: UserResponseDto;

  @ApiProperty({
    enum: NotificationType,
    description: 'Type of notification',
    example: NotificationType.WAR_DECLARED,
  })
  type: NotificationType;

  @ApiProperty({
    description: 'Title of the notification',
    example: 'War Declared!',
  })
  title: string;

  @ApiProperty({
    description: 'Content of the notification',
    example: 'A war has been declared against your region!',
  })
  content: string;

  @ApiProperty({
    description: 'Related entity ID (e.g., war ID)',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  entityId: string;

  @ApiProperty({
    description: 'Related entity type (e.g., "war")',
    example: 'war',
  })
  entityType: string;

  @ApiProperty({
    description: 'Whether the notification has been read',
    example: false,
  })
  isRead: boolean;

  @ApiProperty({
    description: 'Date when the notification was created',
    type: Date,
  })
  createdAt: Date;
}
