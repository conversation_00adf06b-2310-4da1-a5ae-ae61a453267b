import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { UserService } from '../user/user.service';
import { Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { instanceToPlain } from 'class-transformer';
import { MailService } from 'src/mail/mail.service';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import * as crypto from 'crypto';
import { ChatService } from 'src/chat/chat.service';
import { JwtDebugUtil } from '../common/utils/jwt-debug.util';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private userService: UserService,
    private jwtService: JwtService,
    private configService: ConfigService,
    private mailService: MailService,
    private chatService: ChatService
  ) {}

  async validateUser(email: string, password: string) {
    const user = await this.userService.findByEmail(email);
    if (!user) throw new UnauthorizedException('User not found!');

    const passwordValid = await bcrypt.compare(password, user.password);
    if (!passwordValid) throw new UnauthorizedException('Invalid credentials');

    return user;
  }

  async login(email: string, password: string, res: Response) {
    const user = await this.validateUser(email, password);

    const chatsWithUnread = await this.chatService.getTotalUnreadCountForUser(user.id);

    // Ensure username is properly encoded for JWT payload
    const encodedUsername = Buffer.from(user.username, 'utf8').toString('utf8');

    // Debug username encoding for Chinese characters
    if (process.env.NODE_ENV === 'development' || process.env.JWT_DEBUG === 'true') {
      if (user.username !== encodedUsername) {
        JwtDebugUtil.compareUsernames(user.username, encodedUsername, 'Original', 'Encoded');
      }
      JwtDebugUtil.debugUsername(encodedUsername);
    }

    const payload = { userId: user.id, username: encodedUsername };
    const token = await this.jwtService.signAsync(payload);

    // Debug the generated token
    if (process.env.NODE_ENV === 'development' || process.env.JWT_DEBUG === 'true') {
      this.logger.debug(`Generated JWT token for user ${user.id} (${encodedUsername})`);
      JwtDebugUtil.debugJwtToken(token, 'Login Token');
    }

    if (!user.isActive) {
      throw new BadRequestException(
        'This account is not yet activated. Please check your mail for activation!',
      );
    }

    res.cookie('access_token', token, {
      httpOnly: true,
      maxAge: this.configService.get('JWT_EXPIRES_SECONDS') * 1000,
    });

    res.json({ access_token: token, chats: chatsWithUnread,  user: instanceToPlain(user) });
  }

  async sendPasswordResetEmail(forgotPasswordDto: ForgotPasswordDto) {
    const { email } = forgotPasswordDto;

    const updatedUser =
      await this.userService.generatePasswordResetToken(email);
    const emailSubject = 'Reset Your Password!';
    const emailTemplate = 'forgot-password-email';
    const passwordResetLink = `${process.env.CLIENT_URL}/reset-password?token=${updatedUser.resetToken}`;
    const emailContext = {
      username: updatedUser.username,
      passwordResetLink: passwordResetLink,
    };

    return this.mailService.sendMail({
      to: email,
      subject: emailSubject,
      template: emailTemplate,
      context: emailContext,
    });
  }

  async resetPassword(token: string, newPassword: string) {
    const user = await this.userService.findByResetToken(token);
    if (user.resetTokenExpires && user.resetTokenExpires < new Date()) {
      throw new BadRequestException('Token expired');
    }

    // Hash the new password before saving
    user.password = await bcrypt.hash(newPassword, 10);
    user.resetToken = '';
    user.resetTokenExpires = null;
    user.isActive = true;

    await this.userService.update(user.id, user);
    return { message: 'Password has been successfully reset' };
  }

  async verifyAccount(token: string): Promise<any> {
    const user = await this.userService.findByVerificationToken(token);
    if (!user) {
      throw new BadRequestException('Invalid or expired verification token');
    }

    // Mark user as active and remove token
    user.isActive = true;
    user.verificationToken = undefined;
    await this.userService.update(user.id, user);

    return { message: 'Account successfully verified' };
  }

  async resendVerificationEmail(email: string): Promise<{ message: string }> {
    const user = await this.userService.findByEmail(email);
    if (!user) {
      throw new NotFoundException('User with this email does not exist');
    }

    if (user.isActive) {
      throw new BadRequestException('Account is already verified');
    }

    user.verificationToken = crypto.randomBytes(32).toString('hex');
    await this.userService.update(user.id, user);

    // Send the verification email
    await this.mailService.sendMail({
      to: user.email,
      subject: 'Account Verification',
      template: 'verify-account-email',
      context: {
        username: user.username,
        verificationLink: `${process.env.CLIENT_URL}/verify-account?token=${user.verificationToken}`,
      },
    });

    return { message: 'Verification email sent successfully' };
  }
}
