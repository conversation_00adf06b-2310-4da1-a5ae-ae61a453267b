import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WorkSession } from './entity/work-session.entity';

@Injectable()
export class WorkSessionService {
  constructor(
    @InjectRepository(WorkSession)
    private workSessionRepository: Repository<WorkSession>,
  ) {}

  async findAll(): Promise<WorkSession[]> {
    return this.workSessionRepository.find({
      relations: ['factory', 'worker'],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: number): Promise<WorkSession> {
    const workSession = await this.workSessionRepository.findOne({
      where: { id },
      relations: ['factory', 'worker'],
    });
    if (!workSession) {
      throw new NotFoundException(`Work session with ID ${id} not found`);
    }
    return workSession;
  }

  async findByWorker(workerId: number): Promise<WorkSession[]> {
    return this.workSessionRepository.find({
      where: { workerId },
      relations: ['factory'],
      order: { createdAt: 'DESC' },
    });
  }

  async findByFactory(factoryId: number): Promise<WorkSession[]> {
    return this.workSessionRepository.find({
      where: { factoryId },
      relations: ['worker'],
      order: { createdAt: 'DESC' },
    });
  }
}
