import {
  BadRequestException,
  Injectable,
  NotFoundException,
  Inject,
  forwardRef,
  Logger,
  ConflictException,
} from '@nestjs/common';
import { CreateUserDto, UpdateUserDto } from './dto/user.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entity/user.entity';
import * as crypto from 'crypto';
import { TrainDto, TrainingCurrency, TrainingType } from './dto/training.dto';
import { MailService } from 'src/mail/mail.service';
import { RegionService } from 'src/region/region.service';
import { EnergyService } from './energy.service';
import { AutoMode } from './enums/auto-mode.enum';
import { TransferMoneyDto } from './dto/transfer-money.dto';
import {
  FileUploadService,
  UploadedFileInfo,
} from '../storage/file-upload.service';
import { WelcomeService } from 'src/welcome/welcome.service';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly mailService: MailService,
    private readonly regionService: RegionService,
    @Inject(forwardRef(() => EnergyService))
    private readonly energyService: EnergyService,
    private readonly fileUploadService: FileUploadService,
    private readonly welcomeService: WelcomeService
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const region = await this.regionService.findById(createUserDto.regionId);
    const user = this.userRepository.create(createUserDto);

    user.region = region;
    user.verificationToken = crypto.randomBytes(32).toString('hex');
    user.isActive = false;

    const savedUser = await this.userRepository.save(user);

        // Send welcome message to the newly verified user
    try {
      await this.welcomeService.sendWelcomeMessage(user.id);
    } catch (error) {
      console.error(`Failed to send welcome message to user ${user.id}:`, error);
      // Don't fail the verification process if welcome message fails
    }

    await this.mailService.sendMail({
      to: savedUser.email,
      subject: 'Account verification',
      template: 'verify-account-email',
      context: {
        username: savedUser.username,
        verificationLink: `${process.env.CLIENT_URL}/verify-account?token=${savedUser.verificationToken}`,
      },
    });

    return savedUser;
  }

  async findAll(): Promise<User[]> {
    return this.userRepository.find({
      order: {
        level: 'DESC',
      },
    });
  }

  async findAllCount(): Promise<number> {
    return this.userRepository.count();
  }

  async findOne(id: number): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: [
        'region',
        'region.state',
        'region.state.leader',
        'memberOfParty',
        'memberOfParty.members',
        'memberOfParty.region',
        'memberOfParty.leader',
        'leadingParty',
        'leadingParty.members',
        'leadingParty.region',
      ],
    });
    if (!user) throw new NotFoundException('User not found');

    // Update energy before returning the user
    await this.energyService.updateUserEnergy(user);

    const regionWithBorders = await this.regionService.addBordersWithNames(
      user.region,
    );
    user.region = regionWithBorders;
    return user;
  }

  async findByEmail(email: string): Promise<User | null> {
    const user = await this.userRepository.findOne({
      where: { email: email },
      relations: ['region'],
    });
    return user;
  }

  async findByResetToken(token: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { resetToken: token },
    });
    if (!user) throw new NotFoundException('User not found');
    return user;
  }

  async findByVerificationToken(token: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { verificationToken: token },
    });
    if (!user) throw new NotFoundException('User not found');
    return user;
  }

  async findByStripeCustomerId(stripeCustomerId: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { stripeCustomerId },
      relations: ['region'],
    });
    if (!user)
      throw new NotFoundException(
        `User with Stripe customer ID ${stripeCustomerId} not found`,
      );
    return user;
  }

  async findByStripeSubscriptionId(
    stripeSubscriptionId: string,
  ): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { stripeSubscriptionId },
      relations: ['region'],
    });
    if (!user)
      throw new NotFoundException(
        `User with Stripe subscription ID ${stripeSubscriptionId} not found`,
      );
    return user;
  }

  async update(id: number, updateUserDto: UpdateUserDto): Promise<User> {
    const existingUser = await this.userRepository.findOne({
      where: { username: updateUserDto.username },
    });

    await this.userRepository.update(id, updateUserDto);
    return this.findOne(id);
  }

  async updateSubscription(
    id: number,
    subscriptionData: {
      isPremium: boolean;
      stripeSubscriptionId?: string;
      subscriptionStatus?: string;
      premiumExpiresAt?: Date;
      energy?: number;
    },
  ): Promise<User> {
    console.log(`Updating subscription for user ${id}:`, subscriptionData);

    const user = await this.findOne(id);

    if (subscriptionData.isPremium !== undefined)
      user.isPremium = subscriptionData.isPremium;
    if (subscriptionData.stripeSubscriptionId)
      user.stripeSubscriptionId = subscriptionData.stripeSubscriptionId;
    if (subscriptionData.subscriptionStatus)
      user.subscriptionStatus = subscriptionData.subscriptionStatus;
    if (subscriptionData.premiumExpiresAt)
      user.premiumExpiresAt = subscriptionData.premiumExpiresAt;
    if (subscriptionData.energy !== undefined)
      user.energy = subscriptionData.energy;

    const updatedUser = await this.userRepository.save(user);
    console.log(`User ${id} subscription updated successfully:`, {
      isPremium: updatedUser.isPremium,
      subscriptionStatus: updatedUser.subscriptionStatus,
      expiresAt: updatedUser.premiumExpiresAt,
      energy: updatedUser.energy,
    });

    return updatedUser;
  }

  async save(user: User): Promise<User> {
    return this.userRepository.save(user);
  }

  async remove(id: number): Promise<void> {
    await this.userRepository.delete(id);
  }

  async generatePasswordResetToken(email: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { email } });
    if (!user) throw new NotFoundException('User not found');

    const resetToken = crypto.randomBytes(32).toString('hex');
    user.resetToken = resetToken;
    user.resetTokenExpires = new Date(Date.now() + 3600000); // 1 hour expiry

    return await this.userRepository.save(user);
  }

  /**
   * Process training for a given user.
   * This function checks if the user is already training, deducts energy,
   * awards XP, deducts the appropriate currency (gold or money), updates attributes,
   * and sets a training expiry to prevent overlapping sessions.
   */
  async train(userId: string, trainDto: TrainDto): Promise<any> {
    const user = await this.findOne(+userId);
    const now = new Date();

    if (user.trainingExpiresAt && user.trainingExpiresAt > now) {
      throw new BadRequestException(
        'You are already training. Please wait until the current training session finishes.',
      );
    }

    // Check if user has enough currency (gold or money)
    if (trainDto.currency === TrainingCurrency.GOLD) {
      if (user.gold < trainDto.amount) {
        throw new BadRequestException('Not enough gold to train with gold.');
      }
      user.gold -= trainDto.amount;
    } else if (trainDto.currency === TrainingCurrency.MONEY) {
      if (user.money < trainDto.amount) {
        throw new BadRequestException('Not enough money to train with money.');
      }
      user.money -= trainDto.amount;
    }

    // Convert duration from seconds to minutes for energy and XP calculations
    const durationInMinutes = trainDto.duration / 60;

    // Grant XP for training (3 XP per minute of training)
    user.experience += Math.round(3 * durationInMinutes);
    user.checkAndUpdateLevel();

    // Update specific attribute based on training type
    switch (trainDto.trainingType) {
      case TrainingType.STRENGTH:
        user.strength = (user.strength || 0) + 1;
        break;
      case TrainingType.ENDURANCE:
        user.endurance = (user.endurance || 0) + 1;
        break;
      case TrainingType.INTELLIGENCE:
        user.intelligence = (user.intelligence || 0) + 1;
        break;
    }

    // Set training expiry time
    user.trainingExpiresAt = new Date(now.getTime() + trainDto.duration * 1000);

    return this.userRepository.save(user);
  }

  async consumeEnergy(userId: number, amount: number): Promise<User> {
    // Get user with updated energy
    const user = await this.energyService.updateEnergyById(userId);

    // Log the current energy before consumption
    console.log(
      `Before consumption - User ID: ${userId}, Energy: ${user.energy}, Amount to consume: ${amount}`,
    );

    // Check if user has enough energy
    if (user.energy < amount) {
      throw new BadRequestException(
        `Insufficient energy: User has ${user.energy} but needs ${amount}`,
      );
    }

    // Consume energy
    user.energy -= amount;

    // Log the energy after consumption
    console.log(
      `After consumption - User ID: ${userId}, Remaining energy: ${user.energy}`,
    );

    // Save and return the updated user
    return await this.userRepository.save(user);
  }

  async updateAutoMode(
    userId: number,
    autoModeData: {
      activeAutoMode: AutoMode;
      autoTargetId: string | null;
      autoModeExpiresAt: Date | null;
    },
  ): Promise<User> {
    const user = await this.findOne(userId);

    user.activeAutoMode = autoModeData.activeAutoMode;

    // Handle null values with type checking
    user.autoTargetId = autoModeData.autoTargetId as string;
    user.autoModeExpiresAt = autoModeData.autoModeExpiresAt as Date;

    // If auto mode is being disabled (set to NONE), also clear workingAt
    if (autoModeData.activeAutoMode === AutoMode.NONE) {
      user.workingAt = null;
    }

    return this.userRepository.save(user);
  }

  async findUsersWithActiveAutoMode(): Promise<User[]> {
    return this.userRepository.find({
      where: [
        { activeAutoMode: AutoMode.WORK },
        { activeAutoMode: AutoMode.WAR },
      ],
      select: [
        'id',
        'isPremium',
        'activeAutoMode',
        'autoTargetId',
        'autoModeExpiresAt',
      ],
    });
  }

  async transferMoney(transferDto: TransferMoneyDto): Promise<void> {
    const { fromUserId, toUserId, amount } = transferDto;

    // Validate that users are not the same
    if (fromUserId === toUserId) {
      throw new BadRequestException('Cannot transfer money to yourself');
    }

    // Validate amount is positive
    if (amount <= 0) {
      throw new BadRequestException('Amount must be greater than 0');
    }

    // Use a transaction to ensure atomicity
    await this.userRepository.manager.transaction(
      async (transactionManager) => {
        // Find both users in a single transaction with locking to prevent race conditions
        const sender = await transactionManager.findOne(User, {
          where: { id: fromUserId, isActive: true },
          lock: { mode: 'pessimistic_write' },
        });

        if (!sender) {
          throw new NotFoundException(
            `Sender user with ID ${fromUserId} not found or not active`,
          );
        }

        if (sender.level < 5) {
          throw new BadRequestException(
            'You must be level 5 or higher to transfer money!',
          );
        }

        const receiver = await transactionManager.findOne(User, {
          where: { id: toUserId, isActive: true },
          lock: { mode: 'pessimistic_write' },
        });

        if (!receiver) {
          throw new NotFoundException(
            `Receiver user with ID ${toUserId} not found or not active`,
          );
        }

        // Check if sender has sufficient funds
        if (sender.money < amount) {
          throw new ConflictException('Insufficient money balance');
        }

        // Update balances
        sender.money -= amount;
        receiver.money += amount;

        // Save both users within the transaction
        await transactionManager.save(sender);
        await transactionManager.save(receiver);

        this.logger.log(
          `Transfer completed: ${amount} money from user ${fromUserId} to user ${toUserId}`,
        );
      },
    );
  }

  /**
   * Upload and update user's avatar
   */
  async uploadAvatar(
    userId: number,
    file: Express.Multer.File,
  ): Promise<UploadedFileInfo> {
    const user = await this.findOne(userId);

    // Delete old avatar if exists
    if (user.avatarUrl) {
      try {
        await this.fileUploadService.deleteFile(user.avatarUrl);
        this.logger.log(`Deleted old avatar for user ${userId}`);
      } catch (error) {
        this.logger.warn(
          `Failed to delete old avatar for user ${userId}:`,
          error,
        );
        // Continue with upload even if old file deletion fails
      }
    }

    // Upload new avatar
    const uploadResult = await this.fileUploadService.uploadImage(
      file,
      userId,
      {
        maxSizeBytes: 5 * 1024 * 1024, // 5MB
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
        imageOptions: {
          maxWidth: 512,
          maxHeight: 512,
          quality: 85,
          format: 'jpeg',
        },
      },
    );
    console.log('Upload result:', uploadResult);

    // Update user's avatar URL
    user.avatarUrl = uploadResult.url;
    await this.userRepository.save(user);

    this.logger.log(
      `Avatar uploaded successfully for user ${userId}: ${uploadResult.url}`,
    );
    return uploadResult;
  }

  /**
   * Delete user's avatar
   */
  async deleteAvatar(userId: number): Promise<void> {
    const user = await this.findOne(userId);

    if (!user.avatarUrl) {
      throw new BadRequestException('User does not have an avatar to delete');
    }

    try {
      await this.fileUploadService.deleteFile(user.avatarUrl);
      user.avatarUrl = null;
      await this.userRepository.save(user);

      this.logger.log(`Avatar deleted successfully for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to delete avatar for user ${userId}:`, error);
      throw new BadRequestException('Failed to delete avatar');
    }
  }
}
