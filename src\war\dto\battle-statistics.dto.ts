import { ApiProperty } from '@nestjs/swagger';

export class BattleStatisticsDto {
  @ApiProperty({
    description: 'Total damage dealt by attackers',
    example: 25000,
  })
  totalAttackerDamage: number;

  @ApiProperty({
    description: 'Total damage dealt by defenders',
    example: 18000,
  })
  totalDefenderDamage: number;

  @ApiProperty({
    description: 'Number of attacker participants',
    example: 25,
  })
  attackerParticipants: number;

  @ApiProperty({
    description: 'Number of defender participants',
    example: 17,
  })
  defenderParticipants: number;

  @ApiProperty({
    description: 'Damage per energy for attackers',
    example: 10.5,
  })
  attackerEfficiency: number;

  @ApiProperty({
    description: 'Damage per energy for defenders',
    example: 9.8,
  })
  defenderEfficiency: number;

  @ApiProperty({
    description: 'Total energy spent by all participants',
    example: 5000,
  })
  totalEnergySpent: number;

  @ApiProperty({
    description: 'Energy spent by attackers',
    example: 3000,
  })
  attackerEnergySpent: number;

  @ApiProperty({
    description: 'Energy spent by defenders',
    example: 2000,
  })
  defenderEnergySpent: number;
}
