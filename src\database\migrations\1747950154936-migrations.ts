import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1747950154936 implements MigrationInterface {
    name = 'Migrations1747950154936'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "gold" TYPE integer USING "gold"::integer`);
        await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "gold" SET DEFAULT 1000`);

        await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "money" TYPE integer USING "money"::integer`);
        await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "money" SET DEFAULT 10000`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "money" TYPE double precision USING "money"::double precision`);
        await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "money" SET DEFAULT 10000`);

        await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "gold" TYPE double precision USING "gold"::double precision`);
        await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "gold" SET DEFAULT 1000`);
    }

}
