import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsBoolean,
  IsNumber,
  IsArray,
} from 'class-validator';
import { RegionStatus } from '../entity/region.entity';

export class CreateRegionDto {
  @ApiProperty({ example: 'North America' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Timestamp when parliament was created',
    required: false,
    type: String,
    format: 'date-time',
  })
  @IsOptional()
  parliamentCreatedAt?: Date;

  @ApiProperty({
    description: 'Timestamp when autonomy parliament was created',
    required: false,
    type: String,
    format: 'date-time',
  })
  @IsOptional()
  autonomyParliamentCreatedAt?: Date;

  @ApiProperty({ example: 450000, description: 'Initial attack damage' })
  @IsNumber()
  initialAttackDamage: number;

  @ApiProperty({ example: 50000, description: 'Initial defend damage' })
  @IsNumber()
  initialDefendDamage: number;

  @ApiProperty({ example: 0, description: 'Pollution level' })
  @IsNumber()
  pollution: number;

  @ApiProperty({ example: 0.1, description: 'Tax rate' })
  @IsNumber()
  taxRate: number;

  @ApiProperty({ example: 0.05, description: 'Market taxes' })
  @IsNumber()
  marketTaxes: number;

  @ApiProperty({
    description: 'Factory output taxes as a JSON object',
    required: false,
  })
  @IsOptional()
  factoryOutputTaxes?: Record<string, number>;

  @ApiProperty({
    example: true,
    description: 'Indicates if the region has sea access',
  })
  @IsBoolean()
  seaAccess: boolean;

  @ApiProperty({
    description:
      'Resources object with current and max values for each resource',
    required: false,
  })
  @IsOptional()
  resources?: any;

  @ApiProperty({ example: 0, description: 'Health index' })
  @IsNumber()
  healthIndex: number;

  @ApiProperty({ example: 0, description: 'Military index' })
  @IsNumber()
  militaryIndex: number;

  @ApiProperty({ example: 0, description: 'Education index' })
  @IsNumber()
  educationIndex: number;

  @ApiProperty({ example: 0, description: 'Development index' })
  @IsNumber()
  developmentIndex: number;

  @ApiProperty({
    example: false,
    description: 'Flag indicating if residency is required for work',
  })
  @IsBoolean()
  residencyForWork: boolean;

  @ApiProperty({
    description: 'Timestamp of the last revolution',
    required: false,
    type: String,
    format: 'date-time',
  })
  @IsOptional()
  lastRevolution?: Date;

  @ApiProperty({
    description: 'Timestamp of the last coup',
    required: false,
    type: String,
    format: 'date-time',
  })
  @IsOptional()
  lastCoup?: Date;

  @ApiProperty({
    description: 'Array of neighboring region IDs',
    required: false,
    isArray: true,
    type: String,
  })
  @IsOptional()
  @IsArray()
  bordersWith?: string[];

  @ApiProperty({
    example: 0,
    description: 'Regional ranking (e.g., place in buildings rating)',
  })
  @IsNumber()
  topRating: number;

  @ApiProperty({
    enum: RegionStatus,
    default: RegionStatus.INDEPENDENT,
    description: 'Regional status (independent or autonomy)',
  })
  @IsEnum(RegionStatus)
  status: RegionStatus;
}
