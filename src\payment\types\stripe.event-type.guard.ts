import Stripe from 'stripe';

export function isCheckoutSessionCompletedEvent(
  event: Stripe.Event,
): event is Stripe.Event & { data: { object: Stripe.Checkout.Session } } {
  return event.type === 'checkout.session.completed';
}

export function isPaymentIntentSucceededEvent(
  event: Stripe.Event,
): event is Stripe.Event & { data: { object: Stripe.PaymentIntent } } {
  return event.type === 'payment_intent.succeeded';
}

export function isCustomerSubscriptionCreatedEvent(
  event: Stripe.Event,
): event is Stripe.Event & { data: { object: Stripe.Subscription } } {
  return event.type === 'customer.subscription.created';
}

export function isCustomerSubscriptionUpdatedEvent(
  event: Stripe.Event,
): event is Stripe.Event & { data: { object: Stripe.Subscription } } {
  return event.type === 'customer.subscription.updated';
}

export function isCustomerSubscriptionDeletedEvent(
  event: Stripe.Event,
): event is Stripe.Event & { data: { object: Stripe.Subscription } } {
  return event.type === 'customer.subscription.deleted';
}
