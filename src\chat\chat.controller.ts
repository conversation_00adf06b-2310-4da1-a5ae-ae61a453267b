import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  Req,
  HttpStatus,
  HttpCode,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Request } from 'express';
import { ChatService } from './chat.service';
import { CreateChatDto } from './dto/create-chat.dto';
import { SendMessageDto } from './dto/send-message.dto';
import { ChatQueryDto, MessageQueryDto } from './dto/chat-query.dto';
import {
  ChatResponseDto,
  MessageResponseDto,
  PaginatedChatsResponseDto,
  PaginatedMessagesResponseDto,
} from './dto/chat-response.dto';
import { AuthGuard } from '../common/guards/auth.guard';
import { plainToClass } from 'class-transformer';
import { Throttle } from '@nestjs/throttler';

interface AuthenticatedRequest extends Request {
  user: {
    userId: number;
    username: string;
  };
}

@ApiTags('Chat')
@ApiBearerAuth()
@Controller('chats')
@UseGuards(AuthGuard)
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new chat conversation' })
  @ApiResponse({
    status: 201,
    description: 'Chat created successfully',
    type: ChatResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async createChat(
    @Body() createChatDto: CreateChatDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<ChatResponseDto> {
    const chat = await this.chatService.createChat(
      createChatDto,
      req.user.userId,
    );

    return plainToClass(ChatResponseDto, {
      ...chat,
      unreadCount: 0, // New chat has no unread messages
    });
  }

  @Get()
  @ApiOperation({ summary: 'Get user chat conversations with pagination' })
  @ApiResponse({
    status: 200,
    description: 'Chats retrieved successfully',
    type: PaginatedChatsResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of chats to return (1-100)',
    example: 20,
  })
  @ApiQuery({
    name: 'cursor',
    required: false,
    description: 'Cursor for pagination',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  async getUserChats(
    @Query() query: ChatQueryDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<PaginatedChatsResponseDto> {
    const result = await this.chatService.getUserChats(req.user.userId, query);

    // Transform chats and add unread counts
    const transformedChats = await Promise.all(
      result.chats.map(async (chat) => {
        const unreadCount = await this.chatService.getUnreadMessageCount(
          chat.id,
          req.user.userId,
        );

        // Get the last message if exists
        const lastMessage = chat.messages?.[0] || null;

        return plainToClass(ChatResponseDto, {
          ...chat,
          lastMessage: lastMessage
            ? plainToClass(MessageResponseDto, {
                ...lastMessage,
                isRead: lastMessage.isReadBy(req.user.userId),
              })
            : null,
          unreadCount,
        });
      }),
    );

    return {
      chats: transformedChats,
      hasMore: result.hasMore,
      nextCursor: result.nextCursor,
    };
  }

  @Get(':chatId/messages')
  @ApiOperation({ summary: 'Get messages for a specific chat with pagination' })
  @ApiParam({
    name: 'chatId',
    description: 'Chat ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: 200,
    description: 'Messages retrieved successfully',
    type: PaginatedMessagesResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Not a chat participant',
  })
  @ApiResponse({ status: 404, description: 'Chat not found' })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of messages to return (1-100)',
    example: 50,
  })
  @ApiQuery({
    name: 'cursor',
    required: false,
    description: 'Cursor for pagination',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  async getChatMessages(
    @Param('chatId') chatId: string,
    @Query() query: MessageQueryDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<PaginatedMessagesResponseDto> {
    const result = await this.chatService.getChatMessages(
      chatId,
      req.user.userId,
      query,
    );

    const transformedMessages = result.messages.map((message) =>
      plainToClass(MessageResponseDto, {
        ...message,
        isRead: message.isReadBy(req.user.userId),
      }),
    );

    return {
      messages: transformedMessages,
      hasMore: result.hasMore,
      nextCursor: result.nextCursor,
    };
  }

  @Post(':chatId/messages')
  @HttpCode(HttpStatus.CREATED)
  @Throttle({ default: { limit: 30, ttl: 60000 } }) // 30 messages per minute
  @ApiOperation({ summary: 'Send a new message to a chat' })
  @ApiParam({
    name: 'chatId',
    description: 'Chat ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: 201,
    description: 'Message sent successfully',
    type: MessageResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Not a chat participant',
  })
  @ApiResponse({ status: 404, description: 'Chat not found' })
  @ApiResponse({ status: 429, description: 'Too many requests' })
  async sendMessage(
    @Param('chatId') chatId: string,
    @Body() sendMessageDto: SendMessageDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<MessageResponseDto> {
    const message = await this.chatService.sendMessage(
      chatId,
      req.user.userId,
      sendMessageDto,
    );

    return plainToClass(MessageResponseDto, {
      ...message,
      isRead: message.isReadBy(req.user.userId),
    });
  }

  @Put(':chatId/read')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Mark messages as read in a chat' })
  @ApiParam({
    name: 'chatId',
    description: 'Chat ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: 200,
    description: 'Messages marked as read successfully',
    schema: {
      type: 'object',
      properties: {
        markedCount: {
          type: 'number',
          description: 'Number of messages marked as read',
          example: 5,
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Not a chat participant',
  })
  @ApiResponse({ status: 404, description: 'Chat not found' })
  async markMessagesAsRead(
    @Param('chatId') chatId: string,
    @Req() req: AuthenticatedRequest,
  ): Promise<{ markedCount: number }> {
    return this.chatService.markMessagesAsRead(chatId, req.user.userId);
  }

  @Get(':chatId/unread-count')
  @ApiOperation({ summary: 'Get unread message count for a chat' })
  @ApiParam({
    name: 'chatId',
    description: 'Chat ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: 200,
    description: 'Unread count retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        unreadCount: {
          type: 'number',
          description: 'Number of unread messages',
          example: 3,
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Not a chat participant',
  })
  @ApiResponse({ status: 404, description: 'Chat not found' })
  async getUnreadCount(
    @Param('chatId') chatId: string,
    @Req() req: AuthenticatedRequest,
  ): Promise<{ unreadCount: number }> {
    const unreadCount = await this.chatService.getUnreadMessageCount(
      chatId,
      req.user.userId,
    );

    return { unreadCount };
  }

  @Get('user-unread-count')
  @ApiOperation({ summary: 'Get unread message count for a chat' })
  @ApiResponse({
    status: 200,
    description: 'Unread count retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        unreadCount: {
          type: 'number',
          description: 'Number of unread messages',
          example: 3,
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Not a chat participant',
  })
  @ApiResponse({ status: 404, description: 'Chat not found' })
  async getUnreadCountByUser(
    @Req() req: AuthenticatedRequest,
  ): Promise<{ unreadCount: number }> {
    const unreadCount = await this.chatService.getTotalUnreadCountForUser(
      req.user.userId,
    );

    return { unreadCount };
  }

  // @Post('cleanup-duplicates')
  // @HttpCode(HttpStatus.OK)
  // @ApiOperation({ summary: 'Clean up duplicate direct chats (admin only)' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Cleanup completed',
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       cleaned: { type: 'number', description: 'Number of chat pairs processed' },
  //       duplicatesRemoved: { type: 'number', description: 'Number of duplicate chats removed' }
  //     }
  //   }
  // })
  // @ApiResponse({ status: 401, description: 'Unauthorized' })
  // async cleanupDuplicateChats(): Promise<{ cleaned: number; duplicatesRemoved: number }> {
  //   // Note: In a real application, you might want to add admin role checking here
  //   return await this.chatService.cleanupDuplicateDirectChats();
  // }
}
