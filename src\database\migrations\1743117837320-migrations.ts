import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1743117837320 implements MigrationInterface {
  name = 'Migrations1743117837320';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "region" ADD "countryCode" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD "verificationToken" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "user" ADD "regionId" uuid NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "isActive" SET DEFAULT false`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "FK_f1a2565b8f2580a146871cf1142" FOREIGN KEY ("regionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: Query<PERSON>unner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "FK_f1a2565b8f2580a146871cf1142"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "isActive" SET DEFAULT true`,
    );
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "regionId"`);
    await queryRunner.query(
      `ALTER TABLE "user" DROP COLUMN "verificationToken"`,
    );
    await queryRunner.query(`ALTER TABLE "region" DROP COLUMN "countryCode"`);
  }
}
