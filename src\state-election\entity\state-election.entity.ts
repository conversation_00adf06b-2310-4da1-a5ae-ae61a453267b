import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { State } from '../../state/entity/state.entity';
import { User } from '../../user/entity/user.entity';

export enum ElectionStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  COMPLETED = 'completed',
}

@Entity()
export class StateElection {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => State, (state) => state.stateElections, { onDelete: 'CASCADE' })
  state: State;

  @Column({ type: 'enum', enum: ElectionStatus, default: ElectionStatus.PENDING })
  status: ElectionStatus;

  @CreateDateColumn()
  createdAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  startedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  endedAt: Date;

  @Column({ type: 'json', default: [] })
  candidates: { userId: number; username: string; votes: number }[];

  @Column({ type: 'json', default: [] })
  voters: { userId: number; votedFor: number }[];

  @ManyToOne(() => User, { nullable: true })
  winner: User;
}