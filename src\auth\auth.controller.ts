import { Body, Controller, Post, Response, Res } from '@nestjs/common';
import { AuthService } from './auth.service';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserService } from 'src/user/user.service';
import { LoginDto } from './dto/login.dto';
import { Public } from 'src/common/decorators/public.decorator';
import { Response as ResponseType } from 'express';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { CreateUserDto } from 'src/user/dto/user.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { VerificationDto } from './dto/verification-dto';
import { ResendVerificationDto } from './dto/resend-verification.dto';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(
    private authService: AuthService,
    private userService: UserService,
  ) {}

  @Post('register')
  @ApiOperation({ summary: 'Register a new user' })
  @Public()
  async register(@Body() createUserDto: CreateUserDto) {
    return this.userService.create(createUserDto);
  }

  @Post('login')
  @Public()
  @ApiOperation({ summary: 'Login and get JWT token' })
  async login(@Body() loginDto: LoginDto, @Response() res) {
    return this.authService.login(loginDto.email, loginDto.password, res);
  }

  @Post('logout')
  async logout(@Res({ passthrough: true }) res: ResponseType) {
    res.cookie('access_token', '', { expires: new Date() });
  }

  @Post('forgot-password')
  @Public()
  async forgotPassword(
    @Body() forgotPasswordDto: ForgotPasswordDto,
  ): Promise<{ message: string }> {
    await this.authService.sendPasswordResetEmail(forgotPasswordDto);
    return { message: 'Password reset email sent' };
  }

  @Post('reset-password')
  @Public()
  @ApiOperation({ summary: 'Reset password using a valid reset token' })
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    const { token, password } = resetPasswordDto;
    return this.authService.resetPassword(token, password);
  }

  @Post('verify-account')
  @Public()
  @ApiOperation({ summary: 'Verify user account' })
  async verifyAccount(@Body() verificationDto: VerificationDto) {
    return this.authService.verifyAccount(verificationDto.token);
  }

  @Post('resend-verification')
  @ApiOperation({ summary: 'Resend account verification email' })
  async resendVerification(
    @Body() resendVerificationDto: ResendVerificationDto,
  ) {
    return this.authService.resendVerificationEmail(
      resendVerificationDto.email,
    );
  }
}
