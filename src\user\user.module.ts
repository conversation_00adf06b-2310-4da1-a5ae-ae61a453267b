import { UserService } from './user.service';
import { UserController } from './user.controller';
import { Module, forwardRef } from '@nestjs/common';
import { User } from './entity/user.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MailModule } from 'src/mail/mail.module';
import { RegionModule } from 'src/region/region.module';
import { WarModule } from 'src/war/war.module';
import { EnergyService } from './energy.service';
import { PremiumExpirationService } from './premium-expiration.service';
import { StorageModule } from '../storage/storage.module';
import { WelcomeModule } from 'src/welcome/welcome.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    MailModule,
    RegionModule,
    StorageModule,
    WelcomeModule,
    forwardRef(() => WarModule),
  ],
  controllers: [UserController],
  providers: [UserService, EnergyService, PremiumExpirationService],
  exports: [UserService, EnergyService, PremiumExpirationService],
})
export class UserModule {}
