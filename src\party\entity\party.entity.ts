import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Type } from 'class-transformer';
import { Region } from '../../region/entity/region.entity';
import { User } from '../../user/entity/user.entity';
import { PartyJoinRequest } from './party-join-request.entity';

@Entity()
export class Party {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ default: false })
  isActive: boolean;

  @ManyToOne(() => Region, (region) => region.parties)
  region: Region;

  @ManyToOne(() => User, (user) => user.leadingParty)
  @Type(() => User)
  leader: User;

  @OneToMany(() => User, (user) => user.memberOfParty)
  @Type(() => User)
  members: User[];

  @OneToMany(() => PartyJoinRequest, (request) => request.party)
  joinRequests: PartyJoinRequest[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
