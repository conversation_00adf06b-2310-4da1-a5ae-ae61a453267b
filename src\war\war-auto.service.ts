import { Injectable, Logger } from '@nestjs/common';
import { BullMQAutoActionService } from '../shared/bullmq-auto-action.service';
import { AutoMode } from 'src/user/enums/auto-mode.enum';

@Injectable()
export class WarAutoService {
  private readonly logger = new Logger(WarAutoService.name);

  constructor(
    private readonly bullMQAutoActionService: BullMQAutoActionService,
  ) {}

  /**
   * Start auto attack for a user in a war
   * @param userId User ID
   * @param warId War ID
   * @param energyPercentage Energy percentage (kept for API compatibility, not used in current implementation)
   */
  async startAutoAttack(
    userId: number,
    warId: string,
    energyPercentage: number, // eslint-disable-line @typescript-eslint/no-unused-vars
  ): Promise<void> {
    this.logger.log(`Starting auto attack for user ${userId} in war ${warId}`);

    // Start the auto action using BullMQ
    await this.bullMQAutoActionService.startAutoAction(userId, warId, AutoMode.WAR);

    this.logger.log(`Auto attack started for user ${userId} in war ${warId}`);
  }

  /**
   * Stop auto attack for a user in a war
   * @param userId User ID
   * @param warId War ID
   */
  async stopAutoAttack(userId: number, warId: string): Promise<void> {
    this.logger.log(`Stopping auto attack for user ${userId} in war ${warId}`);

    // Stop the auto action using BullMQ
    await this.bullMQAutoActionService.stopAutoAction(userId, warId, AutoMode.WAR);

    this.logger.log(`Auto attack stopped for user ${userId} in war ${warId}`);
  }
}
