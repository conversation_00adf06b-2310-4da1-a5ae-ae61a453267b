import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

export class UpdatePartyDto {
  @ApiProperty({
    description: 'New party name',
    example: 'Revolutionaries',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'New party description',
    example: 'Group of revolutionaries',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;
}
