import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1742495497708 implements MigrationInterface {
  name = 'Migrations1742495497708';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."region_status_enum" AS ENUM('independent', 'autonomy')`,
    );
    await queryRunner.query(
      `CREATE TABLE "region" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "parliamentCreatedAt" TIMESTAMP, "autonomyParliamentCreatedAt" TIMESTAMP, "initialAttackDamage" double precision NOT NULL DEFAULT '0', "initialDefendDamage" double precision NOT NULL DEFAULT '0', "pollution" double precision NOT NULL DEFAULT '0', "taxRate" double precision NOT NULL DEFAULT '0', "marketTaxes" double precision NOT NULL DEFAULT '0', "factoryOutputTaxes" json, "seaAccess" boolean NOT NULL DEFAULT false, "resources" json, "healthIndex" double precision NOT NULL DEFAULT '0', "militaryIndex" double precision NOT NULL DEFAULT '0', "educationIndex" double precision NOT NULL DEFAULT '0', "developmentIndex" double precision NOT NULL DEFAULT '0', "residencyForWork" boolean NOT NULL DEFAULT false, "lastRevolution" TIMESTAMP, "lastCoup" TIMESTAMP, "bordersWith" text, "topRating" integer NOT NULL DEFAULT '0', "status" "public"."region_status_enum" NOT NULL DEFAULT 'independent', CONSTRAINT "PK_5f48ffc3af96bc486f5f3f3a6da" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "party" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "description" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "regionId" uuid, CONSTRAINT "PK_e6189b3d533e140bb33a6d2cec1" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "party_members_user" ("partyId" uuid NOT NULL, "userId" integer NOT NULL, CONSTRAINT "PK_d48bca142048df86053ce88864f" PRIMARY KEY ("partyId", "userId"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2cbea089fee36e210ae7e99ed1" ON "party_members_user" ("partyId") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_795c0b6372930c6e8204de691b" ON "party_members_user" ("userId") `,
    );
    await queryRunner.query(
      `ALTER TABLE "party" ADD CONSTRAINT "FK_57d89c97f170bbe72a23806f6f7" FOREIGN KEY ("regionId") REFERENCES "region"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "party_members_user" ADD CONSTRAINT "FK_2cbea089fee36e210ae7e99ed10" FOREIGN KEY ("partyId") REFERENCES "party"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "party_members_user" ADD CONSTRAINT "FK_795c0b6372930c6e8204de691b2" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "party_members_user" DROP CONSTRAINT "FK_795c0b6372930c6e8204de691b2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "party_members_user" DROP CONSTRAINT "FK_2cbea089fee36e210ae7e99ed10"`,
    );
    await queryRunner.query(
      `ALTER TABLE "party" DROP CONSTRAINT "FK_57d89c97f170bbe72a23806f6f7"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_795c0b6372930c6e8204de691b"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2cbea089fee36e210ae7e99ed1"`,
    );
    await queryRunner.query(`DROP TABLE "party_members_user"`);
    await queryRunner.query(`DROP TABLE "party"`);
    await queryRunner.query(`DROP TABLE "region"`);
    await queryRunner.query(`DROP TYPE "public"."region_status_enum"`);
  }
}
