import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
} from 'typeorm';
import { WarType, WarTarget, WarStatus } from './war.entity';
import { ResourcesCapturedDto } from '../dto/resources-captured.dto';
import { DamageDealerDto } from '../dto/damage-dealer.dto';
import { WarEventDto } from '../dto/war-event.dto';

@Entity()
export class WarHistory {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  warId: string;

  @Column({
    type: 'enum',
    enum: WarType,
  })
  warType: WarType;

  @Column({
    type: 'enum',
    enum: WarTarget,
  })
  warTarget: WarTarget;

  @Column({ type: 'text', nullable: true })
  declaration: string;

  @Column({ nullable: true })
  attackerStateName: string;

  @Column({ nullable: true })
  attackerRegionName: string;

  @Column({ nullable: true })
  defenderStateName: string;

  @Column({ nullable: true })
  defenderRegionName: string;

  @Column({ nullable: true })
  targetRegionName: string;

  @Column()
  declaredByUsername: string;

  @Column()
  declaredAt: Date;

  @Column({ nullable: true })
  startedAt: Date;

  @Column({ nullable: true })
  endedAt: Date;

  @Column('float', { nullable: true })
  durationHours: number;

  @Column({
    type: 'enum',
    enum: WarStatus,
  })
  finalStatus: WarStatus;

  @Column('varchar', { nullable: true })
  winner: 'attacker' | 'defender' | null;

  @Column('float', { default: 0 })
  totalAttackerDamage: number;

  @Column('float', { default: 0 })
  totalDefenderDamage: number;

  @Column('int', { default: 0 })
  totalParticipants: number;

  @Column('int', { default: 0 })
  attackerParticipants: number;

  @Column('int', { default: 0 })
  defenderParticipants: number;

  @Column('int', { default: 0 })
  totalEnergySpent: number;

  @Column('json', { nullable: true })
  resourcesCaptured: ResourcesCapturedDto;

  @Column('json')
  topDamageDealers: DamageDealerDto[];

  @Column('json')
  keyEvents: WarEventDto[];

  @CreateDateColumn()
  createdAt: Date;
}
