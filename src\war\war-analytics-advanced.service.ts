import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { War, WarStatus, WarType, WarTarget } from './entity/war.entity';
import { User } from '../user/entity/user.entity';
import { Region } from '../region/entity/region.entity';
import { State } from '../state/entity/state.entity';
import { WarHistory } from './entity/war-history.entity';
import { WarReport } from './entity/war-report.entity';

export interface DamageLeaderboard {
  userId: number;
  username: string;
  totalDamage: number;
  warCount: number;
  averageDamagePerWar: number;
  highestSingleWarDamage: number;
  side: 'attacker' | 'defender' | 'mixed';
}

export interface EfficiencyMetrics {
  userId: number;
  username: string;
  totalDamage: number;
  totalEnergySpent: number;
  efficiency: number; // damage per energy
  warCount: number;
  winCount: number;
  winRate: number;
}

export interface RegionalPerformance {
  regionId: string;
  regionName: string;
  totalWars: number;
  warsWon: number;
  warsLost: number;
  winRate: number;
  totalDamageDealt: number;
  totalDamageReceived: number;
  damageRatio: number;
  mostActiveWarriors: {
    userId: number;
    username: string;
    damage: number;
  }[];
}

export interface WarTrends {
  timeframe: string; // e.g., "Last 7 days", "Last 30 days", "Last 90 days"
  totalWars: number;
  attackerWinRate: number;
  defenderWinRate: number;
  averageDuration: number;
  averageParticipants: number;
  mostCommonType: WarType | string | null;
  mostCommonTarget: WarTarget | string | null;
}

@Injectable()
export class WarAnalyticsAdvancedService {
  constructor(
    @InjectRepository(War)
    private warRepository: Repository<War>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Region)
    private regionRepository: Repository<Region>,
    @InjectRepository(State)
    private stateRepository: Repository<State>,
    @InjectRepository(WarHistory)
    private warHistoryRepository: Repository<WarHistory>,
    @InjectRepository(WarReport)
    private warReportRepository: Repository<WarReport>,
  ) {}

  async getDamageLeaderboard(limit: number = 10): Promise<DamageLeaderboard[]> {
    // Get all completed wars
    const completedWars = await this.warRepository.find({
      where: { status: WarStatus.ENDED },
    });

    // Track damage by user
    const userDamageMap = new Map<
      number,
      {
        userId: number;
        username: string;
        totalDamage: number;
        warCount: number;
        highestDamage: number;
        attackerDamage: number;
        defenderDamage: number;
      }
    >();

    // Process each war
    for (const war of completedWars) {
      // Process attackers
      const attackers = war.participants?.attackers || [];
      for (const attacker of attackers) {
        const userId = attacker.userId;
        const damage = attacker.damage || 0;

        if (!userDamageMap.has(userId)) {
          const user = await this.userRepository.findOne({
            where: { id: userId },
          });
          userDamageMap.set(userId, {
            userId,
            username: user?.username || 'Unknown',
            totalDamage: 0,
            warCount: 0,
            highestDamage: 0,
            attackerDamage: 0,
            defenderDamage: 0,
          });
        }

        const userDataValue = userDamageMap.get(userId)!;
        userDataValue.totalDamage += damage;
        userDataValue.warCount += 1;
        userDataValue.attackerDamage += damage;

        if (damage > userDataValue.highestDamage) {
          userDataValue.highestDamage = damage;
        }
      }

      // Process defenders
      const defenders = war.participants?.defenders || [];
      for (const defender of defenders) {
        const userId = defender.userId;
        const damage = defender.damage || 0;

        if (!userDamageMap.has(userId)) {
          const user = await this.userRepository.findOne({
            where: { id: userId },
          });
          userDamageMap.set(userId, {
            userId,
            username: user?.username || 'Unknown',
            totalDamage: 0,
            warCount: 0,
            highestDamage: 0,
            attackerDamage: 0,
            defenderDamage: 0,
          });
        }

        const userDataValue = userDamageMap.get(userId)!;
        userDataValue.totalDamage += damage;
        userDataValue.warCount += 1;
        userDataValue.defenderDamage += damage;

        if (damage > userDataValue.highestDamage) {
          userDataValue.highestDamage = damage;
        }
      }
    }

    // Convert to array and calculate averages
    const leaderboard = Array.from(userDamageMap.values()).map((userData) => {
      // Determine primary side
      let side: 'attacker' | 'defender' | 'mixed' = 'mixed';
      if (userData.attackerDamage > userData.defenderDamage * 2) {
        side = 'attacker';
      } else if (userData.defenderDamage > userData.attackerDamage * 2) {
        side = 'defender';
      }

      return {
        userId: userData.userId,
        username: userData.username,
        totalDamage: userData.totalDamage,
        warCount: userData.warCount,
        averageDamagePerWar:
          userData.warCount > 0 ? userData.totalDamage / userData.warCount : 0,
        highestSingleWarDamage: userData.highestDamage,
        side,
      };
    });

    // Sort by total damage and limit
    return leaderboard
      .sort((a, b) => b.totalDamage - a.totalDamage)
      .slice(0, limit);
  }

  async getEfficiencyMetrics(limit: number = 10): Promise<EfficiencyMetrics[]> {
    // This is a more complex calculation that would require tracking energy spent per action
    // For this example, we'll estimate energy spent based on damage (assuming 100 damage costs 1 energy)

    // Get all completed wars
    const completedWars = await this.warRepository.find({
      where: { status: WarStatus.ENDED },
    });

    // Track metrics by user
    const userMetricsMap = new Map<
      number,
      {
        userId: number;
        username: string;
        totalDamage: number;
        totalEnergySpent: number;
        warCount: number;
        winCount: number;
      }
    >();

    // Process each war
    for (const war of completedWars) {
      const attackerWon =
        war.attackerGroundDamage >= war.damageRequirement &&
        war.attackerGroundDamage > war.defenderGroundDamage;

      // Process attackers
      const attackers = war.participants?.attackers || [];
      for (const attacker of attackers) {
        const userId = attacker.userId;
        const damage = attacker.damage || 0;
        const energySpent = Math.round(damage / 100); // Estimate: 100 damage = 1 energy

        if (!userMetricsMap.has(userId)) {
          const user = await this.userRepository.findOne({
            where: { id: userId },
          });
          userMetricsMap.set(userId, {
            userId,
            username: user?.username || 'Unknown',
            totalDamage: 0,
            totalEnergySpent: 0,
            warCount: 0,
            winCount: 0,
          });
        }

        const userMetricsValue = userMetricsMap.get(userId)!;
        userMetricsValue.totalDamage += damage;
        userMetricsValue.totalEnergySpent += energySpent;
        userMetricsValue.warCount += 1;

        if (attackerWon) {
          userMetricsValue.winCount += 1;
        }
      }

      // Process defenders
      const defenders = war.participants?.defenders || [];
      for (const defender of defenders) {
        const userId = defender.userId;
        const damage = defender.damage || 0;
        const energySpent = Math.round(damage / 100); // Estimate: 100 damage = 1 energy

        if (!userMetricsMap.has(userId)) {
          const user = await this.userRepository.findOne({
            where: { id: userId },
          });
          userMetricsMap.set(userId, {
            userId,
            username: user?.username || 'Unknown',
            totalDamage: 0,
            totalEnergySpent: 0,
            warCount: 0,
            winCount: 0,
          });
        }

        const userMetricsValue = userMetricsMap.get(userId)!;
        userMetricsValue.totalDamage += damage;
        userMetricsValue.totalEnergySpent += energySpent;
        userMetricsValue.warCount += 1;

        if (!attackerWon) {
          userMetricsValue.winCount += 1;
        }
      }
    }

    // Convert to array and calculate efficiency
    const efficiencyMetrics = Array.from(userMetricsMap.values())
      .filter((metrics) => metrics.totalEnergySpent > 0) // Avoid division by zero
      .map((metrics) => ({
        userId: metrics.userId,
        username: metrics.username,
        totalDamage: metrics.totalDamage,
        totalEnergySpent: metrics.totalEnergySpent,
        efficiency: metrics.totalDamage / metrics.totalEnergySpent,
        warCount: metrics.warCount,
        winCount: metrics.winCount,
        winRate:
          metrics.warCount > 0
            ? (metrics.winCount / metrics.warCount) * 100
            : 0,
      }));

    // Sort by efficiency and limit
    return efficiencyMetrics
      .sort((a, b) => b.efficiency - a.efficiency)
      .slice(0, limit);
  }

  async getRegionalPerformance(regionId: string): Promise<RegionalPerformance> {
    const region = await this.regionRepository.findOne({
      where: { id: regionId },
    });

    if (!region) {
      throw new Error(`Region with ID ${regionId} not found`);
    }

    // Get wars involving this region
    const wars = await this.warRepository.find({
      where: [
        { attackerRegion: { id: regionId } },
        { defenderRegion: { id: regionId } },
        { targetRegion: { id: regionId } },
      ],
      relations: ['attackerRegion', 'defenderRegion', 'targetRegion'],
    });

    let warsWon = 0;
    let warsLost = 0;
    let totalDamageDealt = 0;
    let totalDamageReceived = 0;

    // Track damage by warrior
    const warriorDamageMap = new Map<
      number,
      {
        userId: number;
        username: string;
        damage: number;
      }
    >();

    // Process each war
    for (const war of wars) {
      const isAttacker = war.attackerRegion?.id === regionId;
      const isDefender = war.defenderRegion?.id === regionId;
      const isTarget = war.targetRegion?.id === regionId;

      if (war.status === WarStatus.ENDED) {
        const attackerWon =
          war.attackerGroundDamage >= war.damageRequirement &&
          war.attackerGroundDamage > war.defenderGroundDamage;

        if ((isAttacker && attackerWon) || (isDefender && !attackerWon)) {
          warsWon += 1;
        } else if (
          (isAttacker && !attackerWon) ||
          (isDefender && attackerWon) ||
          isTarget
        ) {
          warsLost += 1;
        }
      }

      // Calculate damage dealt and received
      if (isAttacker) {
        totalDamageDealt += war.attackerGroundDamage;
        totalDamageReceived += war.defenderGroundDamage;

        // Track attacker warriors
        const attackers = war.participants?.attackers || [];
        for (const attacker of attackers) {
          const userId = attacker.userId;
          const damage = attacker.damage || 0;

          // Check if this user belongs to the region
          const user = await this.userRepository.findOne({
            where: { id: userId, region: { id: regionId } },
            relations: ['region'],
          });

          if (user) {
            if (!warriorDamageMap.has(userId)) {
              warriorDamageMap.set(userId, {
                userId,
                username: user.username,
                damage: 0,
              });
            }

            const warriorData = warriorDamageMap.get(userId)!;
            warriorData.damage += damage;
          }
        }
      }

      if (isDefender) {
        totalDamageDealt += war.defenderGroundDamage;
        totalDamageReceived += war.attackerGroundDamage;

        // Track defender warriors
        const defenders = war.participants?.defenders || [];
        for (const defender of defenders) {
          const userId = defender.userId;
          const damage = defender.damage || 0;

          // Check if this user belongs to the region
          const user = await this.userRepository.findOne({
            where: { id: userId, region: { id: regionId } },
            relations: ['region'],
          });

          if (user) {
            if (!warriorDamageMap.has(userId)) {
              warriorDamageMap.set(userId, {
                userId,
                username: user.username,
                damage: 0,
              });
            }

            const warriorData = warriorDamageMap.get(userId)!;
            warriorData.damage += damage;
          }
        }
      }
    }

    // Get top warriors
    const mostActiveWarriors = Array.from(warriorDamageMap.values())
      .sort((a, b) => b.damage - a.damage)
      .slice(0, 5);

    return {
      regionId,
      regionName: region.name,
      totalWars: wars.length,
      warsWon,
      warsLost,
      winRate: wars.length > 0 ? (warsWon / wars.length) * 100 : 0,
      totalDamageDealt,
      totalDamageReceived,
      damageRatio:
        totalDamageReceived > 0 ? totalDamageDealt / totalDamageReceived : 0,
      mostActiveWarriors,
    };
  }

  async getWarTrends(): Promise<WarTrends[]> {
    const now = new Date();

    // Define timeframes
    const timeframes = [
      {
        name: 'Last 7 days',
        startDate: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      },
      {
        name: 'Last 30 days',
        startDate: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
      },
      {
        name: 'Last 90 days',
        startDate: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000),
      },
    ];

    const trends: WarTrends[] = [];

    for (const timeframe of timeframes) {
      // Get wars in this timeframe
      const wars = await this.warRepository.find({
        where: {
          endedAt: Between(timeframe.startDate, now),
          status: WarStatus.ENDED,
        },
      });

      if (wars.length === 0) {
        trends.push({
          timeframe: timeframe.name,
          totalWars: 0,
          attackerWinRate: 0,
          defenderWinRate: 0,
          averageDuration: 0,
          averageParticipants: 0,
          mostCommonType: null as unknown as WarType,
          mostCommonTarget: null as unknown as WarTarget,
        });
        continue;
      }

      // Calculate statistics
      let attackerWins = 0;
      let totalDuration = 0;
      let totalParticipants = 0;

      // Count war types and targets
      const typeCounts = {};
      const targetCounts = {};

      for (const war of wars) {
        // Count attacker wins
        const attackerWon =
          war.attackerGroundDamage >= war.damageRequirement &&
          war.attackerGroundDamage > war.defenderGroundDamage;

        if (attackerWon) {
          attackerWins += 1;
        }

        // Calculate duration
        if (war.startedAt && war.endedAt) {
          const durationHours =
            (war.endedAt.getTime() - war.startedAt.getTime()) /
            (1000 * 60 * 60);
          totalDuration += durationHours;
        }

        // Count participants
        const attackerCount = war.participants?.attackers?.length || 0;
        const defenderCount = war.participants?.defenders?.length || 0;
        totalParticipants += attackerCount + defenderCount;

        // Count war types
        typeCounts[war.warType] = (typeCounts[war.warType] || 0) + 1;

        // Count war targets
        targetCounts[war.warTarget] = (targetCounts[war.warTarget] || 0) + 1;
      }

      // Find most common type and target
      let mostCommonType: WarType | string | null = null;
      let maxTypeCount = 0;

      for (const [type, count] of Object.entries(typeCounts)) {
        const countValue = count as number;
        if (countValue > maxTypeCount) {
          mostCommonType = type as WarType;
          maxTypeCount = countValue;
        }
      }

      let mostCommonTarget: WarTarget | string | null = null;
      let maxTargetCount = 0;

      for (const [target, count] of Object.entries(targetCounts)) {
        const countValue = count as number;
        if (countValue > maxTargetCount) {
          mostCommonTarget = target as WarTarget;
          maxTargetCount = countValue;
        }
      }

      trends.push({
        timeframe: timeframe.name,
        totalWars: wars.length,
        attackerWinRate: (attackerWins / wars.length) * 100,
        defenderWinRate: ((wars.length - attackerWins) / wars.length) * 100,
        averageDuration: totalDuration / wars.length,
        averageParticipants: totalParticipants / wars.length,
        mostCommonType: mostCommonType,
        mostCommonTarget: mostCommonTarget,
      });
    }

    return trends;
  }
}
