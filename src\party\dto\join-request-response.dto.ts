import { PartyJoinRequest, JoinRequestStatus } from '../entity/party-join-request.entity';

export class JoinRequestResponseDto {
  id: string;
  partyId: string;
  partyName: string;
  userId: number;
  username: string;
  status: JoinRequestStatus;
  createdAt: Date;
  updatedAt: Date;

  static fromEntity(entity: PartyJoinRequest): JoinRequestResponseDto {
    const dto = new JoinRequestResponseDto();
    dto.id = entity.id;
    dto.partyId = entity.party.id;
    dto.partyName = entity.party.name;
    dto.userId = entity.user.id;
    dto.username = entity.user.username || `User ${entity.user.id}`;
    dto.status = entity.status;
    dto.createdAt = entity.createdAt;
    dto.updatedAt = entity.updatedAt;
    return dto;
  }

  static fromEntities(entities: PartyJoinRequest[]): JoinRequestResponseDto[] {
    return entities.map(entity => this.fromEntity(entity));
  }
}