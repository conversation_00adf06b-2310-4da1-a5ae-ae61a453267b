import { MigrationInterface, QueryRunner } from 'typeorm';

export class Migrations1742075160361 implements MigrationInterface {
  name = 'Migrations1742075160361';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "user" ("id" SERIAL NOT NULL, "username" character varying NOT NULL, "email" character varying NOT NULL, "password" character varying NOT NULL, "resetToken" character varying, "resetTokenExpires" TIMESTAMP, "avatarUrl" character varying, "level" integer NOT NULL DEFAULT '1', "experience" integer NOT NULL DEFAULT '0', "energy" integer NOT NULL DEFAULT '100', "strength" integer NOT NULL DEFAULT '0', "endurance" integer NOT NULL DEFAULT '0', "intelligence" integer NOT NULL DEFAULT '0', "perception" integer NOT NULL DEFAULT '0', "aboutMe" text, "isActive" boolean NOT NULL DEFAULT true, CONSTRAINT "UQ_78a916df40e02a9deb1c4b75edb" UNIQUE ("username"), CONSTRAINT "UQ_e12875dfb3b1d92d7d7c5377e22" UNIQUE ("email"), CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "user"`);
  }
}
