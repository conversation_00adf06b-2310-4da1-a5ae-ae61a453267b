import { ApiProperty } from '@nestjs/swagger';
import { Party } from '../entity/party.entity';

export class PartyResponseDto {
  @ApiProperty({ description: 'Party ID', example: 'uuid-string' })
  id: string;

  @ApiProperty({ description: 'Party name', example: 'Freedom Fighters' })
  name: string;

  @ApiProperty({
    description: 'Party description',
    example: 'A group fighting for freedom',
  })
  description: string;

  @ApiProperty({ description: 'Whether the party is active', example: true })
  isActive: boolean;

  @ApiProperty({ description: 'Party leader ID', example: 1 })
  leaderId: number;

  @ApiProperty({ description: 'Party leader username', example: 'john_doe' })
  leaderUsername: string;

  @ApiProperty({ description: 'Region ID', example: 'uuid-string' })
  regionId: string;

  @ApiProperty({ description: 'Region name', example: 'New York' })
  regionName: string;

  @ApiProperty({ description: 'Member count', example: 5 })
  memberCount: number;

  @ApiProperty({
    description: 'Creation date',
    example: '2023-01-01T00:00:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update date',
    example: '2023-01-01T00:00:00Z',
  })
  updatedAt: Date;

  constructor(party: Party) {
    this.id = party.id;
    this.name = party.name;
    this.description = party.description;
    this.isActive = party.isActive;

    if (party.leader) {
      this.leaderId = party.leader.id;
      this.leaderUsername = party.leader.username;
    }

    if (party.region) {
      this.regionId = party.region.id;
      this.regionName = party.region.name;
    }

    this.memberCount = party.members?.length || 0;
    this.createdAt = party.createdAt;
    this.updatedAt = party.updatedAt;
  }

  static fromEntity(party: Party): PartyResponseDto {
    return new PartyResponseDto(party);
  }

  static fromEntities(parties: Party[]): PartyResponseDto[] {
    return parties.map((party) => PartyResponseDto.fromEntity(party));
  }
}
