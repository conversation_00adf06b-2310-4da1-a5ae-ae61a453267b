import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityNotFoundError } from 'typeorm';
import { Notification, NotificationType } from './entity/notification.entity';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { User } from '../user/entity/user.entity';
import { MailService } from '../mail/mail.service';

@Injectable()
export class NotificationService {
  constructor(
    @InjectRepository(Notification)
    private notificationRepository: Repository<Notification>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private mailService: MailService,
  ) {}

  async createNotificationFromDto(
    createNotificationDto: CreateNotificationDto,
    sendEmail: boolean = false,
  ): Promise<Notification> {
    const { userId, type, title, content, entityId, entityType } =
      createNotificationDto;
    const user = await this.findUser(userId);
    return this.createNotification(
      user,
      type,
      title,
      content,
      entityId,
      entityType,
      sendEmail,
    );
  }

  async createNotification(
    user: User,
    type: NotificationType,
    title: string,
    content: string,
    entityId?: string,
    entityType?: string,
    sendEmail: boolean = false,
  ): Promise<Notification> {
    const notification = this.notificationRepository.create({
      user,
      type,
      title,
      content,
      entityId,
      entityType,
      isRead: false,
    });

    const savedNotification =
      await this.notificationRepository.save(notification);

    // Optionally send an email notification
    if (sendEmail) {
      await this.sendEmailNotification(user, title, content, type);
    }

    return savedNotification;
  }

  async createWarNotification(
    users: User[],
    type: NotificationType,
    title: string,
    content: string,
    warId: string,
    sendEmail: boolean = false,
  ): Promise<Notification[]> {
    const notifications: Notification[] = [];

    for (const user of users) {
      const notification = await this.createNotification(
        user,
        type,
        title,
        content,
        warId,
        'war',
        sendEmail,
      );
      notifications.push(notification);
    }

    return notifications;
  }

  async getUserNotifications(userId: number): Promise<Notification[]> {
    return this.notificationRepository.find({
      where: { user: { id: userId } },
      order: { createdAt: 'DESC' },
    });
  }

  async getUnreadNotifications(userId: number): Promise<Notification[]> {
    return this.notificationRepository.find({
      where: { user: { id: userId }, isRead: false },
      order: { createdAt: 'DESC' },
    });
  }

  async markAsRead(notificationId: string): Promise<Notification> {
    const notification = await this.notificationRepository.findOne({
      where: { id: notificationId },
    });

    if (!notification) {
      throw new Error('Notification not found');
    }

    notification.isRead = true;
    return this.notificationRepository.save(notification);
  }

  async markAllAsRead(userId: number): Promise<void> {
    await this.notificationRepository.update(
      { user: { id: userId }, isRead: false },
      { isRead: true },
    );
  }

  private async findUser(userId: string | number): Promise<User> {
    const id = typeof userId === 'string' ? parseInt(userId, 10) : userId;
    try {
      return await this.userRepository.findOneOrFail({ where: { id } });
    } catch (error) {
      if (error instanceof EntityNotFoundError) {
        throw new Error(`User with ID ${userId} not found`);
      }
      throw error;
    }
  }

  private async sendEmailNotification(
    user: User,
    title: string,
    content: string,
    type: NotificationType,
  ): Promise<void> {
    // Only send emails for important notifications
    const importantTypes = [
      NotificationType.WAR_DECLARED,
      NotificationType.WAR_ENDED,
      NotificationType.REGION_CONQUERED,
      NotificationType.STATE_ELECTION,
    ];

    if (importantTypes.includes(type)) {
      await this.mailService.sendMail({
        to: user.email,
        subject: `Warfront Nations: ${title}`,
        template: 'notification-email',
        context: {
          username: user.username,
          title,
          content,
          notificationType: type,
          gameUrl: process.env.CLIENT_URL,
        },
      });
    }
  }
}
