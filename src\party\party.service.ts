import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  ConflictException,
} from '@nestjs/common';
import { PartyResponseDto } from './dto/party-response.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreatePartyDto } from './dto/create-party.dto';
import { UpdatePartyDto } from './dto/update-party.dto';
import { Region } from 'src/region/entity/region.entity';
import { User } from 'src/user/entity/user.entity';
import { UserService } from 'src/user/user.service';
import { Party } from './entity/party.entity';
import { RegionService } from 'src/region/region.service';
import { PartyJoinRequest, JoinRequestStatus } from './entity/party-join-request.entity';
import { CreateJoinRequestDto } from './dto/create-join-request.dto';
import { JoinRequestResponseDto } from './dto/join-request-response.dto';

@Injectable()
export class PartyService {
  constructor(
    @InjectRepository(Party)
    private partyRepository: Repository<Party>,
    @InjectRepository(PartyJoinRequest)
    private joinRequestRepository: Repository<PartyJoinRequest>,
    private readonly regionService: RegionService,
    private readonly userService: UserService,
  ) {}

  async createParty(
    createPartyDto: CreatePartyDto,
    userId: number,
  ): Promise<PartyResponseDto> {
    const region: Region = await this.regionService.findById(
      createPartyDto.regionId,
    );
    if (!region) {
      throw new NotFoundException('Region not found');
    }

    // Get the user data
    const leader = await this.userService.findOne(userId);

    if (leader.gold < 200) {
      throw new ForbiddenException('Not enough gold to create a party.');
    }

    // Check if user is already a leader of another party
    if (leader.leadingParty) {
      throw new ConflictException(
        'You are already a leader of another party. You can only lead one party at a time.',
      );
    }

    // Check if user is already a member of another party
    if (leader.memberOfParty) {
      throw new ConflictException(
        'You are already a member of another party. You can only be a member of one party at a time.',
      );
    }

    // Update the leader's gold balance
    leader.gold -= 200;

    const party = new Party();
    party.name = createPartyDto.name;
    party.description = createPartyDto.description || '';
    party.leader = leader;
    party.isActive = true;
    party.region = region;

    // Save the party first to get an ID
    const savedParty = await this.partyRepository.save(party);

    // Update the leader's party relationships
    leader.leadingParty = savedParty;
    leader.memberOfParty = savedParty; // Leader is also a member
    await this.userService.save(leader);

    return PartyResponseDto.fromEntity(savedParty);
  }

  async getPartyById(id: string): Promise<Party> {
    const party = await this.partyRepository.findOne({
      where: { id },
      relations: ['leader', 'members', 'region'],
    });
    if (!party) {
      throw new NotFoundException('Party not found');
    }
    return party;
  }

  async updateParty(
    id: string,
    updatePartyDto: UpdatePartyDto,
    userId: number,
  ): Promise<PartyResponseDto> {
    // Get the actual Party entity first
    const partyEntity = await this.partyRepository.findOne({
      where: { id },
      relations: ['leader', 'members', 'region'],
    });

    if (!partyEntity) {
      throw new NotFoundException('Party not found');
    }

    // Verify the user is the leader
    if (partyEntity.leader.id !== userId) {
      throw new ForbiddenException(
        'Only the party leader can update the party',
      );
    }
    if (updatePartyDto.name) {
      partyEntity.name = updatePartyDto.name;
    }
    if (updatePartyDto.description) {
      partyEntity.description = updatePartyDto.description;
    }
    const updatedParty = await this.partyRepository.save(partyEntity);
    return PartyResponseDto.fromEntity(updatedParty);
  }

  // Create a join request for a party
  async createJoinRequest(
    createJoinRequestDto: CreateJoinRequestDto,
    userId: number,
  ): Promise<JoinRequestResponseDto> {
    // Get the party
    const party = await this.getPartyById(createJoinRequestDto.partyId);
    
    // Get the user
    const user = await this.userService.findOne(userId);

    // Check if user is already a leader or member of a party
    if (user.leadingParty) {
      throw new ConflictException(
        'You are already a leader of another party. You must leave that party first.',
      );
    }

    if (user.memberOfParty) {
      throw new ConflictException(
        'You are already a member of another party. You must leave that party first.',
      );
    }

    // Check if the user already has a pending request for this party
    const existingRequest = await this.joinRequestRepository.findOne({
      where: {
        party: { id: party.id },
        user: { id: userId },
        status: JoinRequestStatus.PENDING,
      },
    });

    if (existingRequest) {
      throw new ConflictException(
        'You already have a pending request to join this party.',
      );
    }

    // Create a new join request
    const joinRequest = new PartyJoinRequest();
    joinRequest.party = party;
    joinRequest.user = user;
    joinRequest.status = JoinRequestStatus.PENDING;

    const savedRequest = await this.joinRequestRepository.save(joinRequest);

    // Load relations for the response
    const fullRequest = await this.joinRequestRepository.findOne({
      where: { id: savedRequest.id },
      relations: ['party', 'user'],
    });

    if(!fullRequest) {
      throw new NotFoundException('Join request not found');
    }

    return JoinRequestResponseDto.fromEntity(fullRequest);
  }

  // Get all join requests for a party (leader only)
  async getPartyJoinRequests(
    partyId: string,
    userId: number,
  ): Promise<JoinRequestResponseDto[]> {
    // Get the party to check if the user is the leader
    const party = await this.getPartyById(partyId);

    // Verify the user is the leader
    if (party.leader.id !== userId) {
      throw new ForbiddenException(
        'Only the party leader can view join requests',
      );
    }

    // Get all pending join requests for the party
    const requests = await this.joinRequestRepository.find({
      where: {
        party: { id: partyId },
        status: JoinRequestStatus.PENDING,
      },
      relations: ['party', 'user'],
    });

    return JoinRequestResponseDto.fromEntities(requests);
  }

  // Get user's join requests (both sent by the user)
  async getUserJoinRequests(userId: number): Promise<JoinRequestResponseDto[]> {
    const requests = await this.joinRequestRepository.find({
      where: {
        user: { id: userId },
      },
      relations: ['party', 'user'],
    });

    return JoinRequestResponseDto.fromEntities(requests);
  }

  // Accept a join request (leader only)
  async acceptJoinRequest(
    requestId: string,
    leaderId: number,
  ): Promise<Party> {
    const joinRequest = await this.joinRequestRepository.findOne({
      where: { id: requestId },
      relations: ['party', 'party.leader', 'user'],
    });

    if (!joinRequest) {
      throw new NotFoundException('Join request not found');
    }

    if (joinRequest.status !== JoinRequestStatus.PENDING) {
      throw new ConflictException('This request has already been processed');
    }

    // Verify the user processing the request is the party leader
    if (joinRequest.party.leader.id !== leaderId) {
      throw new ForbiddenException(
        'Only the party leader can accept join requests',
      );
    }

    // Get the user who requested to join
    const user = await this.userService.findOne(joinRequest.user.id);

    // Check if the user is now part of another party (might have joined since request)
    if (user.memberOfParty || user.leadingParty) {
      joinRequest.status = JoinRequestStatus.REJECTED;
      await this.joinRequestRepository.save(joinRequest);
      throw new ConflictException(
        'The user is already part of another party and cannot join',
      );
    }

    // Add the user to the party
    joinRequest.status = JoinRequestStatus.ACCEPTED;
    await this.joinRequestRepository.save(joinRequest);

    // Update the user's memberOfParty relationship
    user.memberOfParty = joinRequest.party;
    await this.userService.save(user);

    // Return the updated party
    return await this.getPartyById(joinRequest.party.id);
  }

  // Reject a join request (leader only)
  async rejectJoinRequest(
    requestId: string,
    leaderId: number,
  ): Promise<JoinRequestResponseDto> {
    const joinRequest = await this.joinRequestRepository.findOne({
      where: { id: requestId },
      relations: ['party', 'party.leader', 'user'],
    });

    if (!joinRequest) {
      throw new NotFoundException('Join request not found');
    }

    if (joinRequest.status !== JoinRequestStatus.PENDING) {
      throw new ConflictException('This request has already been processed');
    }

    // Verify the user processing the request is the party leader
    if (joinRequest.party.leader.id !== leaderId) {
      throw new ForbiddenException(
        'Only the party leader can reject join requests',
      );
    }

    // Update the request status
    joinRequest.status = JoinRequestStatus.REJECTED;
    const updatedRequest = await this.joinRequestRepository.save(joinRequest);

    return JoinRequestResponseDto.fromEntity(updatedRequest);
  }

  // Cancel a join request (user who made the request only)
  async cancelJoinRequest(
    requestId: string,
    userId: number,
  ): Promise<JoinRequestResponseDto> {
    const joinRequest = await this.joinRequestRepository.findOne({
      where: { id: requestId },
      relations: ['party', 'user'],
    });

    if (!joinRequest) {
      throw new NotFoundException('Join request not found');
    }

    if (joinRequest.status !== JoinRequestStatus.PENDING) {
      throw new ConflictException('This request has already been processed');
    }

    // Verify the user cancelling is the one who made the request
    if (joinRequest.user.id !== userId) {
      throw new ForbiddenException(
        'Only the user who made the request can cancel it',
      );
    }

    // Delete the request
    await this.joinRequestRepository.remove(joinRequest);

    // Set id back for the response
    joinRequest.id = requestId;
    
    return JoinRequestResponseDto.fromEntity(joinRequest);
  }

  async joinParty(partyId: string, userId: number): Promise<PartyResponseDto> {
    // This method is now deprecated in favor of the join request system
    throw new ForbiddenException(
      'Direct joins are no longer allowed. Please create a join request.',
    );
  }

async leaveParty(partyId: string, userId: number): Promise<PartyResponseDto | null> {
  const partyEntity = await this.partyRepository.findOne({
    where: { id: partyId },
    relations: ['leader', 'members', 'region'],
  });

  if (!partyEntity) {
    throw new NotFoundException('Party not found');
  }

  // Get the user
  const user = await this.userService.findOne(userId);

  if (partyEntity.leader.id === userId) {
    // Leader is leaving - disband the party
    
    // Remove party membership from all members
    for (const member of partyEntity.members) {
      member.memberOfParty = null;
      await this.userService.save(member);
    }
    
    // Remove party membership from leader as well
    user.memberOfParty = null;
    await this.userService.save(user);
    
    // Delete the party
    await this.partyRepository.remove(partyEntity);
    
    // Return null or a specific response indicating party was disbanded
    return null;
  }

  // Regular member leaving
  // Remove user from party members array
  partyEntity.members = partyEntity.members.filter(member => member.id !== userId);
  
  // Remove party membership from user
  user.memberOfParty = null;
  
  // Save both the user and party
  await this.userService.save(user);
  const updatedParty = await this.partyRepository.save(partyEntity);
  
  return PartyResponseDto.fromEntity(updatedParty);
}

  async getPartiesByRegion(regionId: string): Promise<PartyResponseDto[]> {
    const parties = await this.partyRepository.find({
      where: { region: { id: regionId } },
      relations: ['leader', 'members', 'region'],
    });
    return PartyResponseDto.fromEntities(parties);
  }

  async getPartiesByUser(userId: number): Promise<PartyResponseDto[]> {
    const user = await this.userService.findOne(userId);
    const parties: Party[] = [];

    // Check if user is a leader of a party
    if (user.leadingParty) {
      parties.push(user.leadingParty);
    }

    // Check if user is a member of a party
    if (user.memberOfParty) {
      // Only add if it's not the same as the leading party
      if (
        !user.leadingParty ||
        user.leadingParty.id !== user.memberOfParty.id
      ) {
        parties.push(user.memberOfParty);
      }
    }

    return PartyResponseDto.fromEntities(parties);
  }

  async transferLeadership(
    partyId: string,
    currentLeaderId: number,
    newLeaderId: number,
  ): Promise<PartyResponseDto> {
    const partyEntity = await this.partyRepository.findOne({
      where: { id: partyId },
      relations: ['leader', 'members', 'region'],
    });

    if (!partyEntity) {
      throw new NotFoundException('Party not found');
    }

    // Verify current leader
    if (partyEntity.leader.id !== currentLeaderId) {
      throw new ForbiddenException(
        'Only the current party leader can transfer leadership',
      );
    }

    // Find the new leader
    const newLeader = await this.userService.findOne(newLeaderId);

    // Check if new leader is already a leader of another party
    if (newLeader.leadingParty && newLeader.leadingParty.id !== partyId) {
      throw new ConflictException(
        'The new leader is already leading another party. A user can only lead one party at a time.',
      );
    }

    // Check if new leader is a member of this party
    if (newLeader.memberOfParty?.id !== partyId) {
      throw new ForbiddenException(
        'The new leader must be a member of the party',
      );
    }

    // Get the current leader
    const currentLeader = await this.userService.findOne(currentLeaderId);

    // Update the current leader's relationship
    currentLeader.leadingParty = null;
    await this.userService.save(currentLeader);

    // Update the new leader's relationship
    newLeader.leadingParty = partyEntity;
    await this.userService.save(newLeader);

    // Update the party
    partyEntity.leader = newLeader;

    const updatedParty = await this.partyRepository.save(partyEntity);
    return PartyResponseDto.fromEntity(updatedParty);
  }

  async kickMember(
    partyId: string,
    memberId: number,
    leaderId: number,
  ): Promise<Party> {
    const partyEntity = await this.partyRepository.findOne({
      where: { id: partyId },
      relations: ['leader', 'members', 'region'],
    });

    if (!partyEntity) {
      throw new NotFoundException('Party not found');
    }

    // Verify the user performing the action is the leader
    if (partyEntity.leader.id !== leaderId) {
      throw new ForbiddenException(
        'Only the party leader can kick members',
      );
    }

    // Verify the target is not the leader (can't kick yourself)
    if (memberId === leaderId) {
      throw new ForbiddenException(
        'Party leaders cannot kick themselves. Transfer leadership first.',
      );
    }

    // Get the member to be kicked
    const memberToKick = await this.userService.findOne(memberId);

    // Verify the member is actually in this party
    if (!memberToKick.memberOfParty || memberToKick.memberOfParty.id !== partyId) {
      throw new NotFoundException(
        'This user is not a member of your party',
      );
    }

    // Remove party membership
    memberToKick.memberOfParty = null;
    await this.userService.save(memberToKick);

    // Get the updated party
    return await this.getPartyById(partyId);
  }

}