import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../user/entity/user.entity';
import { PaymentTransaction } from './payment-transaction.entity';

@Entity()
export class GoldPurchase {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  goldAmount: number;

  @Column()
  realMoneyAmount: number;

  @Column()
  currency: string;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn()
  user: User;

  @Column()
  userId: number;

  @ManyToOne(() => PaymentTransaction, { eager: true })
  @JoinColumn()
  paymentTransaction: PaymentTransaction;

  @Column()
  paymentTransactionId: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
