import {
  Controller,
  Post,
  Body,
  UseGuards,
  Req,
  Get,
  Param,
  Patch,
  Delete,
} from '@nestjs/common';
import { CreateJoinRequestDto } from './dto/create-join-request.dto';
import { JoinRequestResponseDto } from './dto/join-request-response.dto';
import { PartyResponseDto } from './dto/party-response.dto';
import { PartyService } from './party.service';
import { Party } from './entity/party.entity';

@Controller('party-requests')
export class PartyJoinRequestController {
  constructor(private readonly partyService: PartyService) {}

  @Post()
  createJoinRequest(
    @Body() createJoinRequestDto: CreateJoinRequestDto,
    @Req() req: Request & { user: { userId: number } },
  ): Promise<JoinRequestResponseDto> {
    return this.partyService.createJoinRequest(
      createJoinRequestDto,
      req.user.userId,
    );
  }

  @Get('user')
  getUserJoinRequests(@Req() req: Request & { user: { userId: number } },): Promise<JoinRequestResponseDto[]> {
    return this.partyService.getUserJoinRequests(req.user.userId);
  }

  @Get('party/:partyId')
  getPartyJoinRequests(
    @Param('partyId') partyId: string,
    @Req() req: Request & { user: { userId: number } },
  ): Promise<JoinRequestResponseDto[]> {
    return this.partyService.getPartyJoinRequests(partyId, req.user.userId);
  }

  @Patch(':requestId/accept')
  acceptJoinRequest(
    @Param('requestId') requestId: string,
    @Req() req: Request & { user: { userId: number } },
  ): Promise<Party> {
    return this.partyService.acceptJoinRequest(requestId, req.user.userId);
  }

  @Patch(':requestId/reject')
  rejectJoinRequest(
    @Param('requestId') requestId: string,
    @Req() req: Request & { user: { userId: number } },
  ): Promise<JoinRequestResponseDto> {
    return this.partyService.rejectJoinRequest(requestId, req.user.userId);
  }

  @Delete(':requestId')
  cancelJoinRequest(
    @Param('requestId') requestId: string,
    @Req() req: Request & { user: { userId: number } },
  ): Promise<JoinRequestResponseDto> {
    return this.partyService.cancelJoinRequest(requestId, req.user.userId);
  }
}