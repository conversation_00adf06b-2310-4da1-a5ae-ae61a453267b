import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class TokenRefreshMiddleware implements NestMiddleware {
  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    const token = req.cookies['access_token'];

    if (token) {
      try {
        const decodedToken: any = this.jwtService.verify(token, {
          ignoreExpiration: true,
        });

        const expirationThreshold = 10; // seconds
        if (decodedToken.exp - Date.now() / 1000 < expirationThreshold) {
          // Use the correct payload structure that matches the current auth system
          const newToken = await this.issueNewToken(
            decodedToken.userId,
            decodedToken.username,
          );
          res.cookie('access_token', newToken, {
            httpOnly: true,
            maxAge: this.configService.get('JWT_EXPIRES_SECONDS') * 1000,
          }); // Convert to milliseconds
        }
      } catch (error) {
        console.error('Token verification error:', error);
      }
    }

    next();
  }

  private async issueNewToken(
    userId: number,
    username: string,
  ): Promise<string> {
    // Ensure username is properly encoded for JWT payload
    const encodedUsername = Buffer.from(username, 'utf8').toString('utf8');
    const payload = { userId, username: encodedUsername };
    return await this.jwtService.signAsync(payload);
  }
}
