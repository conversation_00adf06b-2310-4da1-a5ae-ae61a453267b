import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from '../../user/entity/user.entity';
import { Region } from '../../region/entity/region.entity';
import { WorkSession } from '../../work-session/entity/work-session.entity';

export enum FactoryType {
  GOLD = 'GOLD',
  MONEY = 'MONEY',
  // Future resource types can be added here
  // OIL = 'OIL',
  // ORE = 'ORE',
  // URANIUM = 'URANIUM',
  // DIAMONDS = 'DIAMONDS',
}

@Entity()
export class Factory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({
    type: 'enum',
    enum: FactoryType,
    default: FactoryType.MONEY,
  })
  type: FactoryType;

  @Column('uuid')
  regionId: string;

  @Column({ nullable: true })
  ownerId: number | null;

  @Column('decimal', { precision: 10, scale: 2 })
  wage: number;

  @Column()
  maxWorkers: number;
  
  @OneToMany(() => User, (user) => user.workingAt)
  workers: User[];

  @Column()
  energyCost: number;

  @Column('decimal', { precision: 10, scale: 2 })
  resourcePerWork: number;

  @ManyToOne(() => User, (user) => user.ownedFactories)
  owner: User;

  @ManyToOne(() => Region, (region) => region.factories)
  region: Region;

  @OneToMany(() => WorkSession, (workSession) => workSession.factory)
  workSessions: WorkSession[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}


