import { ApiProperty } from '@nestjs/swagger';

export class SubscriptionStatusResponseDto {
  @ApiProperty({
    description: 'Whether the user has an active premium subscription',
    example: true,
  })
  isPremium: boolean;

  @ApiProperty({
    description: 'Status of the subscription',
    example: 'active',
    enum: ['active', 'inactive', 'past_due', 'canceled', 'unpaid', 'trialing'],
    nullable: true,
  })
  status: string | null;

  @ApiProperty({
    description: 'Expiration date of the premium subscription',
    example: '2023-12-31T23:59:59.999Z',
    nullable: true,
  })
  expiresAt: Date | null;
}
