import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { StorageService } from './storage.service';
import * as sharp from 'sharp';

export interface FileUploadOptions {
  maxSizeBytes?: number;
  allowedMimeTypes?: string[];
  imageOptions?: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    format?: 'jpeg' | 'png' | 'webp';
  };
}

export interface UploadedFileInfo {
  url: string;
  key: string;
  size: number;
  mimeType: string;
}

@Injectable()
export class FileUploadService {
  private readonly logger = new Logger(FileUploadService.name);

  constructor(private readonly storageService: StorageService) {}

  /**
   * Upload and process an image file
   */
  async uploadImage(
    file: Express.Multer.File,
    userId: number,
    options: FileUploadOptions = {}
  ): Promise<UploadedFileInfo> {
    // Set default options
    const defaultOptions: FileUploadOptions = {
      maxSizeBytes: 5 * 1024 * 1024, // 5MB
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
      imageOptions: {
        maxWidth: 512,
        maxHeight: 512,
        quality: 85,
        format: 'jpeg',
      },
    };

    const mergedOptions = { ...defaultOptions, ...options };
    mergedOptions.imageOptions = { ...defaultOptions.imageOptions, ...options.imageOptions };

    // Validate file
    this.validateFile(file, mergedOptions);

    try {
      // Process the image
      const processedBuffer = await this.processImage(file.buffer, mergedOptions.imageOptions);

      // Generate unique key
      const key = this.storageService.generateFileKey(userId, file.originalname, 'avatars');

      // Upload to storage
      const url = await this.storageService.uploadFile(
        key,
        processedBuffer,
        `image/${mergedOptions.imageOptions.format}`,
        {
          userId: userId.toString(),
          originalName: file.originalname,
          uploadedAt: new Date().toISOString(),
        }
      );

      this.logger.log(`Image uploaded successfully for user ${userId}: ${key}`);

      return {
        url,
        key,
        size: processedBuffer.length,
        mimeType: `image/${mergedOptions.imageOptions.format}`,
      };
    } catch (error) {
      this.logger.error(`Failed to upload image for user ${userId}:`, error);
      throw new BadRequestException(`Failed to upload image: ${error.message}`);
    }
  }

  /**
   * Delete an uploaded file
   */
  async deleteFile(url: string): Promise<void> {
    try {
      const key = this.storageService.extractKeyFromUrl(url);
      if (!key) {
        throw new Error('Invalid file URL');
      }

      await this.storageService.deleteFile(key);
      this.logger.log(`File deleted successfully: ${key}`);
    } catch (error) {
      this.logger.error(`Failed to delete file ${url}:`, error);
      throw new BadRequestException(`Failed to delete file: ${error.message}`);
    }
  }

  /**
   * Validate uploaded file
   */
  private validateFile(file: Express.Multer.File, options: FileUploadOptions): void {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    // Check file size
    if (options.maxSizeBytes && file.size > options.maxSizeBytes) {
      throw new BadRequestException(
        `File size exceeds limit of ${Math.round(options.maxSizeBytes / 1024 / 1024)}MB`
      );
    }

    // Check MIME type
    if (options.allowedMimeTypes && !options.allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        `Invalid file type. Allowed types: ${options.allowedMimeTypes.join(', ')}`
      );
    }

    // Additional validation for images
    if (file.mimetype.startsWith('image/')) {
      this.validateImageFile(file);
    }
  }

  /**
   * Additional validation for image files
   */
  private validateImageFile(file: Express.Multer.File): void {
    // Check if file buffer looks like an image
    const imageSignatures = [
      [0xFF, 0xD8, 0xFF], // JPEG
      [0x89, 0x50, 0x4E, 0x47], // PNG
      [0x52, 0x49, 0x46, 0x46], // WebP (RIFF)
    ];

    const fileHeader = Array.from(file.buffer.slice(0, 4));
    const isValidImage = imageSignatures.some(signature =>
      signature.every((byte, index) => fileHeader[index] === byte)
    );

    if (!isValidImage) {
      throw new BadRequestException('File does not appear to be a valid image');
    }
  }

  /**
   * Process and optimize image
   */
  private async processImage(
    buffer: Buffer,
    options: FileUploadOptions['imageOptions']
  ): Promise<Buffer> {
    try {
      let sharpInstance = sharp(buffer);

      // Get image metadata
      const metadata = await sharpInstance.metadata();

      // Resize if necessary
      if (options && metadata.width && metadata.height &&
          options.maxWidth && options.maxHeight &&
          (metadata.width > options.maxWidth || metadata.height > options.maxHeight)) {
        sharpInstance = sharpInstance.resize(options.maxWidth, options.maxHeight, {
          fit: 'inside',
          withoutEnlargement: true,
        });
      }

      // Convert to specified format and apply quality
      const format = options?.format || 'jpeg';
      const quality = options?.quality || 85;

      switch (format) {
        case 'jpeg':
          sharpInstance = sharpInstance.jpeg({ quality });
          break;
        case 'png':
          sharpInstance = sharpInstance.png({ quality });
          break;
        case 'webp':
          sharpInstance = sharpInstance.webp({ quality });
          break;
        default:
          sharpInstance = sharpInstance.jpeg({ quality });
      }

      return await sharpInstance.toBuffer();
    } catch (error) {
      this.logger.error('Failed to process image:', error);
      throw new BadRequestException('Failed to process image');
    }
  }
}
