import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from '../../user/entity/user.entity';
import { Region } from '../../region/entity/region.entity';
import { TravelMode } from './travel.entity';

export enum TravelPermissionStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

@Entity()
export class TravelPermission {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User)
  user: User;

  @ManyToOne(() => Region)
  sourceRegion: Region;

  @ManyToOne(() => Region)
  destinationRegion: Region;

  @Column({
    type: 'enum',
    enum: TravelPermissionStatus,
    default: TravelPermissionStatus.PENDING,
  })
  status: TravelPermissionStatus;

  @Column({ type: 'text', nullable: true })
  reason: string;

  @Column({ type: 'text', nullable: true })
  responseMessage: string;

  @ManyToOne(() => User, { nullable: true })
  respondedBy: User;

  @Column({ type: 'timestamp', nullable: true })
  respondedAt: Date;

  @Column({
    type: 'enum',
    enum: TravelMode,
    default: TravelMode.REGULAR,
  })
  travelMode: TravelMode;

  @Column('float', { default: 0 })
  costPaid: number;

  @Column({ default: 'money' })
  currencyUsed: string; // 'money' or 'gold'

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
