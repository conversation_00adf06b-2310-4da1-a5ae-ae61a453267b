import {
  Body,
  Controller,
  Get,
  Post,
  Req,
  Headers,
  RawBodyRequest,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { SkipAuth } from '../auth/decorators/skip-auth.decorator';
import Stripe from 'stripe';
import {
  isCheckoutSessionCompletedEvent,
  isPaymentIntentSucceededEvent,
  isCustomerSubscriptionCreatedEvent,
  isCustomerSubscriptionUpdatedEvent,
  isCustomerSubscriptionDeletedEvent,
} from './types/stripe.event-type.guard';

// Define custom request type with user property
interface AuthenticatedRequest extends Request {
  user: {
    userId: number;
    username: string;
  };
}
import { PaymentService } from './payment.service';
import { StripeService } from './stripe.service';
import { CreatePaymentIntentDto } from './dto/create-payment-intent.dto';
import { PaymentIntentResponseDto } from './dto/payment-intent-response.dto';
import { CreateCheckoutSessionDto } from './dto/create-checkout-session.dto';
import { CheckoutSessionResponseDto } from './dto/checkout-session-response.dto';
import { SubscriptionStatusResponseDto } from './dto/subscription-status-response.dto';

@ApiTags('Payments')
@Controller('payments')
export class PaymentController {
  private readonly logger = new Logger(PaymentController.name);

  constructor(
    private readonly paymentService: PaymentService,
    private readonly stripeService: StripeService,
  ) {}

  @Post('gold/create-intent')
  @ApiOperation({ summary: 'Create a payment intent for gold purchase' })
  @ApiResponse({
    status: 201,
    description: 'Payment intent created successfully',
    type: PaymentIntentResponseDto,
  })
  async createPaymentIntent(
    @Req() req: AuthenticatedRequest,
    @Body() createPaymentIntentDto: CreatePaymentIntentDto,
  ): Promise<PaymentIntentResponseDto> {
    const userId = req.user.userId;
    return this.paymentService.createPaymentIntent(
      userId,
      createPaymentIntentDto,
    );
  }

  @Post('premium/create-session')
  @ApiOperation({
    summary: 'Create a checkout session for premium subscription',
  })
  @ApiResponse({
    status: 201,
    description: 'Checkout session created successfully',
    type: CheckoutSessionResponseDto,
  })
  async createCheckoutSession(
    @Req() req: AuthenticatedRequest,
    @Body() createCheckoutSessionDto: CreateCheckoutSessionDto,
  ): Promise<CheckoutSessionResponseDto> {
    const userId = req.user.userId;
    return this.paymentService.createCheckoutSession(
      userId,
      createCheckoutSessionDto,
    );
  }

  @Get('premium/verify')
  @ApiOperation({ summary: 'Verify premium subscription status' })
  @ApiResponse({
    status: 200,
    description: 'Subscription status retrieved successfully',
    type: SubscriptionStatusResponseDto,
  })
  async verifySubscription(
    @Req() req: AuthenticatedRequest,
  ): Promise<SubscriptionStatusResponseDto> {
    const userId = req.user.userId;
    return this.paymentService.verifySubscription(userId);
  }

  @Post('webhook')
  @ApiOperation({ summary: 'Handle Stripe webhook events' })
  @SkipAuth() // Skip authentication for webhook endpoint
  async handleWebhook(
    @Headers('stripe-signature') signature: string,
    @Req() req: RawBodyRequest<Request>,
  ): Promise<{ received: boolean }> {
    this.logger.log('Webhook received');
    this.logger.log(`Headers: ${JSON.stringify(req.headers)}`);

    if (!signature) {
      this.logger.error('Missing stripe-signature header');
      throw new BadRequestException('Missing stripe-signature header');
    }

    // Access the raw body from the request object
    const rawBody = req['rawBody'];
    if (!rawBody) {
      this.logger.error('Missing request body');
      throw new BadRequestException('Missing request body');
    }

    this.logger.log(`Raw body length: ${rawBody.length} bytes`);

    try {
      this.logger.log('Constructing Stripe event from payload');
      const event = await this.stripeService.constructEventFromPayload(
        signature,
        rawBody,
      );
      this.logger.log(`Webhook event type: ${event.type}`);
      this.logger.log(`Event data: ${JSON.stringify(event.data.object)}`);

      // Handle the event based on its type
      switch (event.type) {
        case 'checkout.session.completed':
          if (isCheckoutSessionCompletedEvent(event)) {
            const session = event.data.object;

            this.logger.log('Processing checkout.session.completed event');
            if (session.mode === 'subscription') {
              if (session.subscription) {
                const subscription = await this.stripeService.getSubscription(
                  session.subscription as string,
                );
                await this.paymentService.handleSubscriptionCreated(
                  subscription,
                );
              }
            } else if (session.mode === 'payment') {
              this.logger.log('Processing gold purchase checkout session');
              await this.paymentService.handleGoldCheckoutCompleted(session);
            }
          }
          break;

        case 'customer.subscription.created':
          if (isCustomerSubscriptionCreatedEvent(event)) {
            const subscription = event.data.object;
            this.logger.log('Processing customer.subscription.created event');
            await this.paymentService.handleSubscriptionCreated(subscription);
          }
          break;

        case 'customer.subscription.updated':
          if (isCustomerSubscriptionUpdatedEvent(event)) {
            const subscription = event.data.object;
            this.logger.log('Processing customer.subscription.updated event');
            await this.paymentService.handleSubscriptionUpdated(subscription);
          }
          break;

        case 'customer.subscription.deleted':
          if (isCustomerSubscriptionDeletedEvent(event)) {
            const subscription = event.data.object;
            this.logger.log('Processing customer.subscription.deleted event');
            await this.paymentService.handleSubscriptionDeleted(subscription);
          }
          break;

        case 'payment_intent.succeeded':
          if (isPaymentIntentSucceededEvent(event)) {
            const paymentIntent = event.data.object;
            this.logger.log('Processing payment_intent.succeeded event');

            if (paymentIntent.metadata?.isFromDirectPayment === 'true') {
              this.logger.log('Handling direct payment (non-checkout session)');
              await this.paymentService.handlePaymentIntentSucceeded(
                paymentIntent,
              );
            } else {
              this.logger.log(
                `Skipping payment_intent.succeeded for checkout session payment: ${paymentIntent.id}`,
              );
            }
          }
          break;

        default:
          this.logger.log(`Unhandled event type: ${event.type}`);
      }

      this.logger.log('Webhook processed successfully');
      return { received: true };
    } catch (err) {
      this.logger.error(`Error handling webhook: ${err.message}`, err.stack);
      throw new BadRequestException(`Webhook error: ${err.message}`);
    }
  }

  @Post('gold/create-session')
  @ApiOperation({ summary: 'Create a checkout session for gold purchase' })
  @ApiResponse({
    status: 201,
    description: 'Checkout session created successfully',
    type: CheckoutSessionResponseDto,
  })
  async createGoldCheckoutSession(
    @Req() req: AuthenticatedRequest,
    @Body() createPaymentIntentDto: CreatePaymentIntentDto,
  ): Promise<CheckoutSessionResponseDto> {
    const userId = req.user.userId;
    return this.paymentService.createGoldCheckoutSession(
      userId,
      createPaymentIntentDto,
    );
  }

  @Post('premium/cancel')
  @ApiOperation({ summary: 'Cancel premium subscription' })
  @ApiResponse({
    status: 200,
    description: 'Subscription cancelled successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
      },
    },
  })
  async cancelSubscription(
    @Req() req: AuthenticatedRequest,
  ): Promise<{ success: boolean; message: string }> {
    this.logger.log(
      `Cancellation request received for user: ${req.user.userId}`,
    );

    await this.paymentService.cancelSubscription(req.user.userId);

    return {
      success: true,
      message:
        'Your subscription has been canceled and will end at the current billing period',
    };
  }
}
