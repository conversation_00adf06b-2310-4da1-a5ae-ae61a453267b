import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1747511307421 implements MigrationInterface {
    name = 'Migrations1747511307421'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."party_join_request_status_enum" AS ENUM('pending', 'accepted', 'rejected')`);
        await queryRunner.query(`CREATE TABLE "party_join_request" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" "public"."party_join_request_status_enum" NOT NULL DEFAULT 'pending', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "partyId" uuid, "userId" integer, CONSTRAINT "PK_8bfd4d483b6f4c112fdb6eda14e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "party_join_request" ADD CONSTRAINT "FK_f520977d4e8335528473da816d7" FOREIGN KEY ("partyId") REFERENCES "party"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "party_join_request" ADD CONSTRAINT "FK_c63e5237cbe97a44b39e436b85f" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "party_join_request" DROP CONSTRAINT "FK_c63e5237cbe97a44b39e436b85f"`);
        await queryRunner.query(`ALTER TABLE "party_join_request" DROP CONSTRAINT "FK_f520977d4e8335528473da816d7"`);
        await queryRunner.query(`DROP TABLE "party_join_request"`);
        await queryRunner.query(`DROP TYPE "public"."party_join_request_status_enum"`);
    }

}
