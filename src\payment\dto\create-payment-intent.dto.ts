import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Min,
  IsUrl,
} from 'class-validator';

export enum GoldPackage {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
  EXTRA_LARGE = 'extra_large',
  CUSTOM = 'custom',
}

export class CreatePaymentIntentDto {
  @ApiProperty({
    description: 'Gold package type',
    enum: GoldPackage,
    example: GoldPackage.MEDIUM,
  })
  @IsEnum(GoldPackage)
  @IsNotEmpty()
  package: GoldPackage;

  @ApiProperty({
    description:
      'Amount of gold to purchase (only required for custom package)',
    example: 1000,
    required: false,
  })
  @IsNumber()
  @Min(100)
  @IsOptional()
  goldAmount?: number;

  @ApiProperty({
    description: 'Currency for the payment',
    example: 'usd',
    default: 'usd',
  })
  @IsString()
  @IsOptional()
  currency?: string = 'usd';

  @ApiProperty({
    description: 'URL to redirect after successful payment',
    example: 'https://example.com/success',
    required: false,
  })
  @IsString()
  @IsOptional()
  successUrl?: string;

  @ApiProperty({
    description: 'URL to redirect after canceled payment',
    example: 'https://example.com/cancel',
    required: false,
  })
  @IsString()
  @IsOptional()
  cancelUrl?: string;
}
