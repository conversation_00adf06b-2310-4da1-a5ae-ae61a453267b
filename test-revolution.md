# Revolution War Implementation Test Guide

## Overview
This document outlines how to test the newly implemented revolution war feature.

## Revolution War Requirements Implemented

### ✅ Cost and Requirements
- **500 gold cost** - Validated in `validateRevolutionWar()`
- **Level requirement** - All 3 participants must be level 5+
- **Duration** - 24 hours (same as regular wars)

### ✅ Damage Calculation
- **New formula**: PlayerPower = (STR × 2) + (INT × 1.5) + (END × 1)
- **Total defense**: Sum of PlayerPower for all stationed players in target region
- **Implemented in**: `calculateRevolutionDamageRequirement()`

### ✅ Post-Revolution Logic
- **Defenders win**: No changes to region ownership
- **Attackers win**: 
  1. Create temporary state named "{region.name} State"
  2. Transfer region to new state
  3. Trigger immediate state elections
  4. New state has open borders enabled

### ✅ Additional Features
- **4-day cooldown** per region (tracked in `lastRevolutionAt`)
- **Citizens-only participation** - Only citizens of target region can participate
- **Open borders** - Revolution states allow unrestricted travel

## API Endpoints

### Declare Revolution War
```http
POST /wars/declare
Authorization: Bearer <token>
Content-Type: application/json

{
  "warType": "revolution",
  "warTarget": "revolution",
  "declaration": "Revolution for freedom!",
  "attackerRegionId": "<region-id>",
  "defenderRegionId": "<same-region-id>"
}
```

### Participate in Revolution
```http
POST /wars/:warId/participate
Authorization: Bearer <token>
Content-Type: application/json

{
  "energyToSpend": 50
}
```

## Testing Steps

1. **Setup**: Create users with level 5+ in a region
2. **Declare**: User declares revolution in their own region
3. **Validate**: Check gold deduction (500) and cooldown timestamp
4. **Participate**: Citizens participate in revolution
5. **Victory**: If attackers win, verify state creation and elections
6. **Travel**: Test open borders functionality

## Database Changes

### New Fields Added
- `state.hasOpenBorders` (boolean, default: false)
- `region.lastRevolutionAt` (timestamp, nullable)

### Migration
- File: `src/database/migrations/1748000000000-add-revolution-fields.ts`
- Run: `npm run migration:run`

## Key Implementation Files

1. **War Service**: `src/war/war.service.ts`
   - Revolution validation
   - Damage calculation
   - Post-war processing

2. **State Service**: `src/state/state.service.ts`
   - Revolution state creation

3. **Travel Service**: `src/travel/travel.service.ts`
   - Open borders support

4. **Entities**: 
   - `src/state/entity/state.entity.ts`
   - `src/region/entity/region.entity.ts`

## Error Handling

The implementation includes comprehensive validation:
- Insufficient gold
- Wrong region citizenship
- Level requirements
- Cooldown violations
- Minimum participant requirements
