import { ApiProperty } from '@nestjs/swagger';

export class VisualDataDto {
  @ApiProperty({
    description: 'URL to the war map',
    example:
      'https://example.com/war-maps/123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  mapUrl?: string;

  @ApiProperty({
    description: 'URL to the damage chart',
    example:
      'https://example.com/war-charts/123e4567-e89b-12d3-a456-426614174000/damage',
    required: false,
  })
  damageChartUrl?: string;

  @ApiProperty({
    description: 'URL to the participation chart',
    example:
      'https://example.com/war-charts/123e4567-e89b-12d3-a456-426614174000/participation',
    required: false,
  })
  participationChartUrl?: string;
}
